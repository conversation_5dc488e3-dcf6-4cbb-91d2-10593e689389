{"name": "lxtv", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-http": "~2.5.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "^2.3.0", "@tauri-apps/plugin-store": "~2.3.0", "artplayer": "^5.2.3", "axios": "^1.10.0", "element-plus": "^2.10.2", "hls.js": "^1.6.7", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.3"}}