# lxtv - 桌面端看剧应用

这是一个使用 Tauri v2, Vue 3 和 Element-Plus 构建的跨平台桌面端看剧应用。

## 技术栈

- **框架**: [Tauri](https://tauri.app/)
- **后端**: Rust
- **前端**: Vue 3 (Composition API) + JavaScript
- **UI 库**: [Element Plus](https://element-plus.org/)
- **数据库**: SQLite (通过 `tauri-plugin-sql-api` 在前端管理)
- **包管理器**: pnpm

---

## 开发设置

在开始之前，请确保您已经安装了 [Node.js](https://nodejs.org/) (推荐 pnpm), [Rust](https://www.rust-lang.org/tools/install) 以及 Tauri 的系统依赖。

1. **安装 Tauri CLI**

    ```bash
    cargo install tauri-cli
    ```

2. **克隆项目**

    ```bash
    git clone <your-repo-url>
    cd lxtv
    ```

3. **安装前端依赖**

    ```bash
    pnpm install
    ```

4. **启动开发环境**

    ```bash
    pnpm tauri dev
    ```

    此命令会同时启动前端开发服务器和后端 Tauri 应用。

---

## 前端架构

前端代码位于 `src` 目录，采用 Vue 3 和 Composition API 开发。

### 数据库管理

数据库操作集中在 `src/db` 目录下:

- `src/db/index.js`: 处理数据库的初始
- `src/db/videoSource.js`: 实现视频源的增删改查操作。

---

## 如何新增一个功能模块

1. 在 `src/db/` 目录下创建一个新文件 (例如 `history.js`)，定义数据库操作函数。
2. 如果需要，在 `src/components/` 目录中创建相关的 Vue 组件。
3. 在 `src/views/` 目录下创建视图组件，并在路由中注册。
