use serde::{Deserialize, Serialize};
use std::fs;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use tauri_plugin_store::StoreBuilder;

// 1. 定义你的配置结构体
#[derive(Clone, Serialize, Deserialize)]
pub struct AppSettings {
    is_initialized: bool,
    theme: String,
    language: String,
}

#[derive(Clone, Serialize, Deserialize)]
pub struct Resource {
    api: String,
    name: String,
}

// 2. 为你的配置实现 Default trait，这就是你的"默认模板"
impl Default for AppSettings {
    fn default() -> Self {
        Self {
            is_initialized: false, // 默认是未初始化状态
            theme: "light".to_string(),
            language: "zh-CN".to_string(),
        }
    }
}

// 获取默认资源列表
pub fn default_resources() -> Vec<Resource> {
    vec![
        Resource {
            api: "http://caiji.dyttzyapi.com".to_string(),
            name: "电影天堂资源".to_string(),
        },
        Resource {
            api: "https://cj.rycjapi.com".to_string(),
            name: "如意资源".to_string(),
        },
        Resource {
            api: "https://json.heimuer.xyz".to_string(),
            name: "黑木耳".to_string(),
        },
        Resource {
            api: "https://bfzyapi.com".to_string(),
            name: "暴风资源".to_string(),
        },
        Resource {
            api: "https://tyyszy.com".to_string(),
            name: "天涯资源".to_string(),
        },
        Resource {
            api: "http://ffzy5.tv".to_string(),
            name: "非凡影视".to_string(),
        },
        Resource {
            api: "https://360zy.com".to_string(),
            name: "360资源".to_string(),
        },
        Resource {
            api: "http://lzizy.net".to_string(),
            name: "量子资源".to_string(),
        },
        Resource {
            api: "https://wolongzyw.com".to_string(),
            name: "卧龙资源".to_string(),
        },
        Resource {
            api: "https://jszyapi.com".to_string(),
            name: "极速资源".to_string(),
        },
        Resource {
            api: "https://dbzy.tv".to_string(),
            name: "豆瓣资源".to_string(),
        },
        Resource {
            api: "https://mozhuazy.com".to_string(),
            name: "魔爪资源".to_string(),
        },
        Resource {
            api: "https://www.mdzyapi.com".to_string(),
            name: "魔都资源".to_string(),
        },
        Resource {
            api: "https://api.zuidapi.com".to_string(),
            name: "最大资源".to_string(),
        },
        Resource {
            api: "https://suonizy.net/".to_string(),
            name: "索尼资源".to_string(),
        },
        Resource {
            api: "https://wwzy.tv".to_string(),
            name: "旺旺短剧".to_string(),
        },
        Resource {
            api: "https://www.suboziyuan.net".to_string(),
            name: "速播资源".to_string(),
        },
    ]
}

// 设置文件的名称
pub const SETTINGS_FILE: &str = "settings.json";

// 只负责初始化设置
pub fn setup_app_settings(app_handle: &AppHandle) -> Result<(), tauri_plugin_store::Error> {
    // 设置存储路径
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .expect("Failed to get app data directory");

    // 确保目录存在
    if !app_dir.exists() {
        fs::create_dir_all(&app_dir).expect("Failed to create app data directory");
    }

    let settings_path = app_dir.join(SETTINGS_FILE);
    let store_result = StoreBuilder::new(app_handle, settings_path).build();

    // 检查存储是否需要初始化
    match store_result {
        Ok(store) => {
            // 检查并初始化设置
            if !store.has("settings") {
                // 存储设置
                let default_settings = AppSettings::default();
                store.set("settings", serde_json::to_value(default_settings).unwrap());
                println!("Default settings initialized");
            }

            // 检查并初始化资源
            if !store.has("resources") {
                // 存储资源
                let default_resources = default_resources();
                store.set(
                    "resources",
                    serde_json::to_value(default_resources).unwrap(),
                );
                println!("Default resources initialized");
            }

            // 保存到文件
            store.save()?;
            println!("Settings loaded successfully");

            Ok(())
        }
        Err(err) => {
            // 如果创建存储失败，返回错误
            eprintln!("Failed to create store");
            Err(err)
        }
    }
}
