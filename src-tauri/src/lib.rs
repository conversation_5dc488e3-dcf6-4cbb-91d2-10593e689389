// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/

mod config;
mod migrations_db;

use config::setup_app_settings;
use migrations_db::get_migrations;

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_opener::init())
        .plugin(
            tauri_plugin_sql::Builder::default()
                .add_migrations(
                    "sqlite:lxtv.db", // 数据库连接字符串，和 tauri.conf.json preload 保持一致
                    get_migrations(),
                )
                .build(),
        )
        .setup(|app| {
            // 只在后端进行设置初始化，更新和获取将在前端实现
            let app_handle = app.handle();
            if let Err(e) = setup_app_settings(&app_handle) {
                eprintln!("Error initializing app settings: {}", e);
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![greet])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
