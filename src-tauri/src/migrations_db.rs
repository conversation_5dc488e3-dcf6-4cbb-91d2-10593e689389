use tauri_plugin_sql::{Migration, MigrationKind};
// Import the necessary items from the include_dir crate
use include_dir::{include_dir, Dir};

// Embed the migrations directory into your binary at compile time.
// The path is relative to your Cargo.toml file (the project root).
static MIGRATIONS_DIR: Dir = include_dir!("$CARGO_MANIFEST_DIR/migrations");

pub fn get_migrations() -> Vec<Migration> {
    let mut migrations: Vec<Migration> = Vec::new();
    for file in MIGRATIONS_DIR.files() {
        let path = file.path();

        // Get the filename without the .sql extension (e.g., "0000_create_students_table")
        let filename_stem = path
            .file_stem()
            .and_then(|s| s.to_str())
            .expect("Failed to get file stem");

        // Split the filename to get the version and description
        // e.g., "0000" and "create_students_table"
        let (version_str, description) = filename_stem.split_once('_').expect(&format!(
            "Invalid migration filename format: {}",
            path.display()
        ));

        // Parse the version string into a number
        let version = version_str.parse::<i64>().expect(&format!(
            "Invalid version number in filename: {}",
            path.display()
        ));

        // Get the SQL content from the embedded file
        let sql = file
            .contents_utf8()
            .expect("Failed to read migration file content as UTF-8");
        // Create the Migration object and add it to our list
        migrations.push(Migration {
            version,
            description: description.into(), // .into() converts &str to String
            sql: sql.into(),                 // .into() converts &str to String
            kind: MigrationKind::Up,
        });
    }

    // It's good practice to sort by version, although the plugin itself handles ordering.
    migrations.sort_by_key(|m| m.version);
    return migrations;
}
