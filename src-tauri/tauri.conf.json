{"$schema": "https://schema.tauri.app/config/2", "productName": "灵象TV", "version": "0.1.0", "identifier": "lxtv.lingxiangtools.top", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "灵象TV", "width": 1200, "height": 800, "resizable": true, "decorations": true, "center": true, "visible": true, "alwaysOnTop": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"sql": {"preload": ["sqlite:lxtv.db"]}, "store": null, "shell": {"open": true}}}