[package]
name = "lxtv"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "lxtv_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2.0.0-alpha", features = [] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tauri-plugin-opener = "2.0.0-alpha"
tauri-plugin-sql = { version = "2.0.0-alpha", features = ["sqlite"] }
thiserror = "2.0.12"
include_dir = "0.7.4"
tauri-plugin-store = "2"
tauri-plugin-http = "2"


