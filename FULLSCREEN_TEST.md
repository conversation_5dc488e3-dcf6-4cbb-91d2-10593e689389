# 全屏功能测试指南

## 测试环境
- Tauri + Vue3 + Artplayer
- 桌面应用环境

## 测试步骤

### 1. 基本全屏功能测试

**测试用例1：点击全屏按钮**
1. 打开播放器页面
2. 点击播放器控制栏的全屏按钮
3. 预期结果：
   - Tauri窗口进入全屏模式
   - Artplayer启用网页全屏
   - 播放器占满整个屏幕
   - 显示"已进入全屏模式"提示

**测试用例2：F11快捷键**
1. 在播放器页面按F11键
2. 预期结果：与测试用例1相同

**测试用例3：自定义全屏按钮**
1. 点击自定义的全屏按钮
2. 预期结果：与测试用例1相同

### 2. 退出全屏功能测试

**测试用例4：ESC键退出**
1. 在全屏模式下按ESC键
2. 预期结果：
   - 退出Artplayer网页全屏
   - 退出Tauri窗口全屏
   - 恢复正常窗口模式

**测试用例5：再次点击全屏按钮**
1. 在全屏模式下再次点击全屏按钮
2. 预期结果：与测试用例4相同

**测试用例6：再次按F11键**
1. 在全屏模式下再次按F11键
2. 预期结果：与测试用例4相同

### 3. 状态同步测试

**测试用例7：窗口全屏状态同步**
1. 进入全屏模式
2. 通过系统方式（如macOS的绿色按钮）退出全屏
3. 预期结果：Artplayer的网页全屏也应该同步退出

### 4. 自动全屏功能测试

**测试用例8：自动全屏设置**
1. 在设置中启用"自动全屏"
2. 播放新的视频
3. 预期结果：视频开始播放后自动进入全屏模式

### 5. 选集列表交互测试

**测试用例9：全屏时打开选集列表**
1. 在全屏模式下点击"选集"按钮
2. 预期结果：
   - 自动退出全屏模式
   - 显示选集列表

## 常见问题排查

### 问题1：点击全屏按钮没有反应
- 检查Tauri权限配置
- 确认`core:window:allow-set-fullscreen`权限已添加

### 问题2：只有窗口全屏，播放器没有占满屏幕
- 检查Artplayer的`fullscreenWeb`配置是否为true
- 检查fullscreenWeb事件监听器是否正常工作

### 问题3：退出全屏时状态不同步
- 检查窗口resize事件监听器
- 确认handleWindowFullscreenChange函数正常执行

## 成功标准

✅ 所有测试用例都能正常执行
✅ 全屏进入和退出流畅无卡顿
✅ 状态同步正确
✅ 快捷键响应正常
✅ 自动全屏功能正常
✅ 与其他功能（如选集列表）交互正常

## 注意事项

- 测试时确保播放器已完全加载
- 在不同操作系统上测试（Windows、macOS、Linux）
- 测试不同分辨率下的表现
- 确保音视频播放正常
