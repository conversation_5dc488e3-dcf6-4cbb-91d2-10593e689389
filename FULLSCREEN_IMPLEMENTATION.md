# Tauri + Artplayer 全屏功能实现说明

## 问题描述
在Tauri桌面应用中，Artplayer的原生全屏功能可能不被支持，用户点击全屏按钮时会提示"不支持全屏"。

## 解决方案
我们实现了一个自定义的全屏功能，结合了Tauri窗口全屏和Artplayer网页全屏：

### 实现步骤
1. **软件全屏**：首先使用Tauri的`setFullscreen(true)`让整个应用窗口全屏
2. **网页全屏**：然后启用Artplayer的`fullscreenWeb`让播放器占满整个窗口

### 核心代码实现

```javascript
// 监听Artplayer的全屏事件
artPlayer.value.on('fullscreen', async (isFullscreen) => {
  if (isFullscreen) {
    // 进入全屏：先软件全屏，再网页全屏
    await appWindow.setFullscreen(true);
    setTimeout(() => {
      artPlayer.value.fullscreenWeb = true;
    }, 100);
  } else {
    // 退出全屏：先退出网页全屏，再退出软件全屏
    artPlayer.value.fullscreenWeb = false;
    setTimeout(async () => {
      await appWindow.setFullscreen(false);
    }, 100);
  }
});
```

### 功能特性
1. **多种触发方式**：
   - 点击播放器全屏按钮
   - 按F11键
   - 按ESC键退出全屏
   - 自定义全屏按钮

2. **权限配置**：
   在`src-tauri/capabilities/default.json`中添加了必要的权限：
   ```json
   "core:window:allow-set-fullscreen",
   "core:window:allow-is-fullscreen"
   ```

3. **用户体验优化**：
   - 进入/退出全屏时显示提示信息
   - 平滑的过渡效果
   - 错误处理和降级方案

### 使用方法
用户可以通过以下方式进入全屏：
1. 点击播放器控制栏的全屏按钮
2. 按F11键
3. 点击自定义的全屏按钮

退出全屏：
1. 按ESC键
2. 再次点击全屏按钮
3. 再次按F11键

### 技术细节
- 使用`setTimeout`确保窗口全屏和网页全屏的正确顺序
- 监听`webfullscreen`事件处理用户通过ESC退出网页全屏的情况
- 添加错误处理确保功能的稳定性
- 样式优化确保全屏时的最佳显示效果
