# Tauri + Artplayer 全屏功能实现说明

## 问题描述
在Tauri桌面应用中，Artplayer的原生全屏功能可能不被支持，用户点击全屏按钮时会提示"不支持全屏"。

## 解决方案
我们实现了一个结合Tauri窗口全屏和Artplayer网页全屏的解决方案：

### 实现原理
1. **禁用原生全屏**：设置`fullscreen: false`禁用Artplayer的原生全屏功能
2. **启用网页全屏**：设置`fullscreenWeb: true`启用Artplayer的网页全屏功能
3. **双重全屏**：当用户触发全屏时，先让Tauri窗口全屏，再启用Artplayer的网页全屏

### 核心代码实现

```javascript
// 播放器配置
const playerOptions = {
  fullscreen: false,  // 禁用原生全屏功能
  fullscreenWeb: true,  // 启用网页全屏功能
  // ... 其他配置
};

// 监听Artplayer的fullscreenWeb事件
artPlayer.value.on('fullscreenWeb', async (isFullscreenWeb) => {
  if (isFullscreenWeb) {
    // 进入网页全屏时，同时设置Tauri窗口全屏
    const isTauriFullscreen = await appWindow.isFullscreen();
    if (!isTauriFullscreen) {
      await appWindow.setFullscreen(true);
    }
  } else {
    // 退出网页全屏时，同时退出Tauri窗口全屏
    const isTauriFullscreen = await appWindow.isFullscreen();
    if (isTauriFullscreen) {
      await appWindow.setFullscreen(false);
    }
  }
});
```

### 功能特性

1. **多种触发方式**：
   - 点击播放器控制栏的全屏按钮
   - 按F11键切换全屏
   - 按ESC键退出全屏
   - 自定义全屏按钮

2. **权限配置**：
   在`src-tauri/capabilities/default.json`中添加了必要的权限：
   ```json
   "core:window:allow-set-fullscreen",
   "core:window:allow-is-fullscreen"
   ```

3. **用户体验优化**：
   - 自动同步Tauri窗口全屏和Artplayer网页全屏状态
   - 平滑的全屏切换体验
   - 错误处理和状态同步

### 使用方法

**进入全屏**：
1. 点击播放器控制栏的全屏按钮
2. 按F11键
3. 点击自定义的全屏按钮

**退出全屏**：
1. 按ESC键
2. 再次点击全屏按钮
3. 再次按F11键

### 技术细节

- **状态同步**：通过监听`fullscreenWeb`事件自动同步Tauri窗口和播放器的全屏状态
- **双重保障**：结合Tauri窗口全屏和Artplayer网页全屏，确保最佳的全屏体验
- **事件处理**：正确处理各种全屏触发方式和退出方式
- **样式优化**：针对fullscreenWeb模式优化播放器样式
