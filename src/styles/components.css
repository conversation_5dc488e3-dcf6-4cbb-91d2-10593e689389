/* 组件样式 - 使用CSS变量适配明暗主题 */

/* 侧边栏项目样式 */
.sidebar-item {
  color: var(--text-color-secondary);
  transition: background 0.2s, color 0.2s;
  padding: 10px 16px 10px 20px;
  margin: 4px 8px;
  border-radius: var(--border-radius);
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.sidebar-item:hover {
  background: var(--background-hover);
  color: var(--primary-color);
}

.sidebar-item.active {
  background: rgba(76, 175, 80, 0.08);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  position: relative;
}

.sidebar-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 8px;
  width: 4px;
  border-radius: 4px;
  background: var(--primary-color);
}

/* 暗色主题下的特殊样式覆盖 */
html.dark .sidebar-item.active {
  background: rgba(76, 175, 80, 0.15);
}

/* 电影卡片样式 */
.movie-card {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s, box-shadow 0.3s;
  background: var(--background-paper);
}

.movie-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--hover-shadow);
}

.movie-card .movie-info {
  padding: 10px;
  background-color: var(--background-paper);
}

.movie-card .movie-title {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.movie-card .movie-rating {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 4px;
  padding: 2px 6px;
  font-weight: bold;
}

html.dark .movie-card .movie-rating {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 卡片和面板样式 */
.card {
  background-color: var(--background-paper);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color-light);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

html.dark a:hover {
  color: var(--primary-light);
}

/* 边框样式 */
.border {
  border: 1px solid var(--border-color);
}

/* 按钮基础样式 */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--card-shadow);
}

html.dark .btn-primary:hover {
  background-color: var(--primary-light);
} 