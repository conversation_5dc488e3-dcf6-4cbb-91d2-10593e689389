/* 主题变量 - 合并亮色和暗色主题 */

:root {
  /* 主色调 - 清新绿色系 */
  --primary-color: #4CAF50;
  --primary-light: #80E27E;
  --primary-dark: #087f23;
  --secondary-color: #2196F3;
  --accent-color: #FF4081;
  
  /* 文字颜色 */
  --text-color: #131313;
  --text-color-secondary: #26262f;
  --text-color-disabled: #C0C4CC;
  
  /* 背景颜色 */
  --background-color: #f8fbff;
  --background-paper: #ffffff;
  --background-gradient: linear-gradient(120deg, #e0f7fa 0%, #f5f5f5 100%);
  --background-hover: #f0f7ff;
  --background-active: #e6f0fa;
  --background-overlay: #ffffff;
  
  /* 边框和分割线 */
  --border-color: #DCDFE6;
  --border-color-light: #EBEEF5;
  --divider-color: #EBEEF5;
  
  /* 阴影 */
  --shadow-color: rgba(0, 0, 0, 0.1);
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  
  /* 布局尺寸 */
  --sidebar-width: 180px;
  --header-height: 64px;
  --border-radius: 8px;
  
  /* Element Plus 变量覆盖 - 清新风格 */
  --el-color-primary: var(--primary-color);
  --el-color-primary-light-3: var(--primary-light);
  --el-color-primary-light-5: #a8e6a8;
  --el-color-primary-light-7: #c8f5c8;
  --el-color-primary-light-8: #e8f8e8;
  --el-color-primary-light-9: #f0faf0;
  --el-color-primary-dark-2: var(--primary-dark);
  
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-error: #f56c6c;
  --el-color-info: #909399;
  
  /* 背景色 */
  --el-bg-color: var(--background-paper);
  --el-bg-color-page: var(--background-color);
  --el-bg-color-overlay: var(--background-overlay);
  
  /* 文字颜色 */
  --el-text-color-primary: var(--text-color);
  --el-text-color-regular: var(--text-color);
  --el-text-color-secondary: var(--text-color-secondary);
  --el-text-color-placeholder: var(--text-color-disabled);
  --el-text-color-disabled: var(--text-color-disabled);
  
  /* 边框颜色 */
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color-light);
  --el-border-color-lighter: #f0f0f0;
  --el-border-color-extra-light: #fafafa;
  
  /* 填充色 */
  --el-fill-color: #f5f7fa;
  --el-fill-color-light: #fafafa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color-darker: #e6e8eb;
  
  /* 阴影 */
  --el-box-shadow: var(--card-shadow);
  --el-box-shadow-light: var(--card-shadow);
  --el-box-shadow-lighter: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --el-box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  
  /* 菜单相关 */
  --el-menu-bg-color: var(--background-paper);
  --el-menu-text-color: var(--text-color);
  --el-menu-active-color: var(--primary-color);
  --el-menu-hover-bg-color: var(--background-hover);
  --el-menu-border-color: var(--border-color);
  
  /* 按钮相关 */
  --el-button-hover-bg-color: var(--primary-light);
  --el-button-hover-text-color: #ffffff;
  --el-button-hover-border-color: var(--primary-light);
  --el-button-active-bg-color: var(--primary-dark);
  --el-button-active-border-color: var(--primary-dark);
  
  /* 输入框相关 */
  --el-input-bg-color: var(--background-paper);
  --el-input-border-color: var(--border-color);
  --el-input-hover-border-color: var(--primary-color);
  --el-input-focus-border-color: var(--primary-color);
  
  /* 表格相关 */
  --el-table-bg-color: var(--background-paper);
  --el-table-tr-bg-color: var(--background-paper);
  --el-table-header-bg-color: var(--background-hover);
  --el-table-border-color: var(--border-color);
  
  /* 对话框相关 */
  --el-dialog-bg-color: var(--background-paper);
  --el-dialog-box-shadow: var(--card-shadow);
  
  /* 抽屉相关 */
  --el-drawer-bg-color: var(--background-paper);
  
  /* 消息框相关 */
  --el-message-bg-color: var(--background-paper);
  --el-message-border-color: var(--border-color);
  
  /* 通知相关 */
  --el-notification-bg-color: var(--background-paper);
  --el-notification-border-color: var(--border-color);
  
  /* 工具提示相关 */
  --el-tooltip-bg-color: #303133;
  --el-tooltip-text-color: #ffffff;
  --el-tooltip-border-color: #303133;
}

/* 暗色主题变量覆盖 */
html.dark {
  /* 主色调 - 保持清新绿色系 */
  --primary-color: #4CAF50;
  --primary-light: #80E27E;
  --primary-dark: #087f23;
  --secondary-color: #42A5F5;
  --accent-color: #FF4081;
  
    /* 文字颜色 */
  --text-color: #E5EAF3;
  --text-color-secondary: #A3A6AD;
  --text-color-disabled: #6C6C6C;
  
  /* 设置按钮颜色，和主题色一致 */
  --el-button-hover-bg-color: var(--primary-light);
  --el-button-hover-text-color: #ffffff;
  --el-button-hover-border-color: var(--primary-light);
  --el-button-active-bg-color: var(--primary-dark);
  --el-button-active-border-color: var(--primary-dark);
  
  
  /* 背景颜色 */
  --background-color: #0a0a0a;
  --background-paper: #1a1a1a;
  --background-gradient: linear-gradient(120deg, #1a237e 0%, #0a0a0a 100%);
  --background-hover: #2c2c2c;
  --background-active: #333333;
  --background-overlay: #242424;
  
  /* 边框和分割线 */
  --border-color: #363637;
  --border-color-light: #404040;
  --divider-color: #333333;
  
  /* 阴影 */
  --shadow-color: rgba(0, 0, 0, 0.5);
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.4);
  --hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.6);
  
  /* Element Plus 变量覆盖 - 暗色清新风格 */
  --el-color-primary: var(--primary-color);
  --el-color-primary-dark-2: var(--primary-dark);
  --el-color-primary-light-3: var(--primary-light);
  --el-color-primary-light-5: #6bc26b;
  --el-color-primary-light-7: #8cd28c;
  --el-color-primary-light-8: #a8e2a8;
  --el-color-primary-light-9: #c4f2c4;
  
  /* 填充色 */
  --el-fill-color: #2a2a2a;
  --el-fill-color-light: #333333;
  --el-fill-color-lighter: #3a3a3a;
  --el-fill-color-extra-light: #404040;
  --el-fill-color-dark: #1a1a1a;
  --el-fill-color-darker: #0a0a0a;
  
  /* 边框颜色 */
  --el-border-color-lighter: #4a4a4a;
  --el-border-color-extra-light: #5a5a5a;
  
  /* 阴影 */
  --el-box-shadow-lighter: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 6px rgba(0, 0, 0, 0.2);
  --el-box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.4), 0 0 6px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
html.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #777;
} 