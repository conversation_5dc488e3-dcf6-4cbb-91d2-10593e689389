# 主题系统说明

本项目采用了基于Element Plus的清新风格主题系统，支持明暗两种主题模式。

## 文件结构

```
src/styles/
├── variables.css     # 所有主题变量（包含亮色和暗色）
├── components.css    # 组件样式（使用CSS变量）
├── common.css        # 通用样式（不依赖主题）
└── README.md         # 本文件
```

## 主题特性

### 清新风格设计
- **主色调**: 清新的绿色系 (#4CAF50)
- **辅助色**: 蓝色 (#2196F3) 和粉色 (#FF4081)
- **背景**: 渐变背景，提供舒适的视觉体验
- **圆角**: 统一的8px圆角设计

### 明暗主题支持
- **亮色主题**: 浅色背景，深色文字，适合白天使用
- **暗色主题**: 深色背景，浅色文字，适合夜间使用
- **自动切换**: 支持跟随系统主题自动切换

### Element Plus集成
- 完全兼容Element Plus组件库
- 自定义CSS变量覆盖默认样式
- 支持所有Element Plus组件的主题适配

## CSS变量说明

### 主要变量
- `--primary-color`: 主色调
- `--background-color`: 页面背景色
- `--background-paper`: 卡片背景色
- `--text-color`: 主要文字颜色
- `--border-color`: 边框颜色

### Element Plus变量
- `--el-color-primary`: Element Plus主色
- `--el-bg-color`: Element Plus背景色
- `--el-text-color-primary`: Element Plus主要文字色
- `--el-border-color`: Element Plus边框色

## 使用方法

### 1. 主题切换
```javascript
import { setTheme, toggleTheme } from '@/utils/themeUtils';

// 设置特定主题
setTheme('dark'); // 或 'light'

// 切换主题
toggleTheme();
```

### 2. 在组件中使用
```vue
<template>
  <div class="card">
    <h2>标题</h2>
    <p>内容</p>
  </div>
</template>

<style scoped>
.card {
  background-color: var(--background-paper);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}
</style>
```

### 3. 自定义样式
```css
/* 使用主题变量 */
.my-component {
  background-color: var(--background-paper);
  color: var(--text-color);
  border-radius: var(--border-radius);
}

/* 特殊情况下的暗色主题覆盖 */
html.dark .my-component {
  /* 暗色主题特殊样式 */
}
```

## 主题切换机制

1. **初始化**: 应用启动时自动检测系统主题
2. **本地存储**: 用户选择会保存到localStorage
3. **系统跟随**: 支持跟随系统主题自动切换
4. **手动切换**: 用户可以通过界面手动切换主题

## 优化特性

### 导航栏悬浮优化
- 亮色主题: 浅绿色悬浮背景
- 暗色主题: 深色悬浮背景
- 统一的主色调指示器

### 动画效果
- 平滑的主题切换动画
- 卡片悬浮效果
- 按钮交互反馈

### 无障碍支持
- 高对比度设计
- 清晰的视觉层次
- 符合WCAG标准的颜色搭配

## 扩展指南

### 添加新的主题变量
1. 在`variables.css`的`:root`选择器中添加亮色变量
2. 在`variables.css`的`html.dark`选择器中添加对应的暗色变量
3. 在组件中使用新变量

### 自定义主题色
```css
:root {
  --primary-color: #your-color;
  --primary-light: #your-light-color;
  --primary-dark: #your-dark-color;
}
```

### 添加新的组件样式
```css
/* 在components.css中添加 */
.my-new-component {
  background-color: var(--background-paper);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

/* 如果需要暗色主题特殊样式 */
html.dark .my-new-component {
  /* 暗色主题特殊样式 */
}
```

## 注意事项

1. 所有颜色都应该使用CSS变量，避免硬编码
2. 只有在必要时才为暗色主题添加特殊覆盖样式
3. 动画效果应该平滑自然
4. 确保足够的对比度以保证可读性 