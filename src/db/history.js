import { getDb } from "./index"

export async function getHistory(vodName='', offset = 0, limit = 10) {
    const db = await getDb()
    if (vodName) {
        const resources = await db.select(`
            select * from history_play where vodName = $1 order by updateAt desc limit $2 offset $3
        `, [vodName, limit, offset])
        return resources
    } else {
        const resources = await db.select(`
            select * from history_play order by updateAt desc limit $1 offset $2
        `, [limit, offset])
        return resources
    }
}


export async function deleteHistory(id) {
    const db = await getDb()
    await db.execute(`
        delete from history_play where id = $1
    `, [id])
}

export async function addHistory(history) {
    const db = await getDb()
    await db.execute(`
        insert into history_play (vodName, vodYear, source, episode, videoUrl, movie, currentPlaybackTime, createdAt, updateAt) values ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [history.vodName, history.vodYear, history.source, history.episode, history.videoUrl, history.movie, history.currentPlaybackTime, history.createdAt, history.updateAt])
}

export async function updateHistory(id, history) {
    const db = await getDb()
    await db.execute(`
        update history_play set vodName = $1, vodYear = $2, source = $3, episode = $4, videoUrl = $5, movie = $6, currentPlaybackTime = $7, updateAt = $8 where id = $9
    `, [history.vodName, history.vodYear, history.source, history.episode, history.videoUrl, history.movie, history.currentPlaybackTime, history.updateAt, id])
}


export async function getHistoryByNameYear(vodName, vodYear) {
    const db = await getDb()
    const history = await db.select(`
        select * from history_play where vodName = $1 and vodYear = $2
    `, [vodName, vodYear])
    return history
}