import { getDb } from "./index"

export async function addResource(resource) {
    const db = await getDb()
    const createdAt = Date.now()
    await db.execute(`
        insert into resources (name, api, status, isDefault, createdAt) values ($1, $2, $3, $4, $5)
    `, [
        resource.name, 
        resource.api, 
        resource.status === undefined ? true : resource.status, 
        resource.isDefault || false, 
        createdAt
    ])
    return true
}

export async function getResources(offset = 0, limit = 10) {
    const db = await getDb()
    const resources = await db.select(`
        select * from resources order by createdAt desc limit $1 offset $2
    `, [limit, offset])
    
    // Convert string 'true'/'false' to boolean values
    return resources.map(resource => ({
        ...resource,
        status: resource.status === 'true' || resource.status === true,
        isDefault: resource.isDefault === 'true' || resource.isDefault === true
    }))
}

export async function getResource(id) {
    const db = await getDb()
    const resource = await db.select(`
        select * from resources where id = $1
    `, [id])
    
    if (resource && resource.length > 0) {
        return {
            ...resource[0],
            status: resource[0].status === 'true' || resource[0].status === true,
            isDefault: resource[0].isDefault === 'true' || resource[0].isDefault === true
        }
    }
    
    return null
}

export async function getDefaultResource() {
    const db = await getDb()
    const resource = await db.select(`
        select * from resources where isDefault = 'true' limit 1
    `)
    
    if (resource && resource.length > 0) {
        return {
            ...resource[0],
            status: resource[0].status === 'true' || resource[0].status === true,
            isDefault: resource[0].isDefault === 'true' || resource[0].isDefault === true
        }
    }
    
    return null
}

export async function updateResource(id, resource) {
    const db = await getDb()
    await db.execute(`
        update resources set name = $1, api = $2, status = $3 where id = $4
    `, [resource.name, resource.api, resource.status, id])
    return true
}

export async function updateResourceDefault(id, isDefault) {
    const db = await getDb()
    // 如果设置为默认，先将所有资源设为非默认
    if (isDefault) {
        await db.execute(`update resources set isDefault = false`)
    }
    // 更新指定资源的默认状态
    await db.execute(`
        update resources set isDefault = $1 where id = $2
    `, [isDefault, id])
    return true
}

export async function deleteResource(id) {
    const db = await getDb()
    await db.execute(`
        delete from resources where id = $1
    `, [id])
    return true
}

export async function countResources() {
    const db = await getDb()
    const result = await db.select(`
        select count(*) as count from resources
    `)
    return result[0].count
}

export async function getResourceList() {
    const db = await getDb()
    const resources = await db.select(`
        select * from resources where status = 'true'
    `)
    return resources
}