import { defineStore } from 'pinia';
import { setTheme, getTheme } from '../utils/themeUtils';
import { load } from '@tauri-apps/plugin-store';
const RESOURCES_FILE = 'settings.json';
let storeInstance = null;

const getStore = async () => {
  if (!storeInstance) {
    storeInstance = await load(RESOURCES_FILE, { autoSave: false });
  }
  return storeInstance;
};

async function getSettings() {
  try {
    const store = await getStore();
    let settings = await store.get('settings');
    return settings;
  } catch (error) {
    console.error('Failed to get settings:', error);
    return null;
  }
}

export const useSettingsStore = defineStore('settings', {
  state: () => ({
    settings: null,
    loading: false,
    error: null
  }),
  
  getters: {
    theme: (state) => state.settings?.theme || getTheme(),
    language: (state) => state.settings?.language || 'zh-CN',
    isInitialized: (state) => state.settings?.is_initialized || false
  },
  
  actions: {
    async fetchSettings() {
      try {
        this.loading = true;
        const data = await getSettings();
        this.settings = data;
        this.error = null;
      } catch (err) {
        this.error = '加载设置失败';
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    
    async saveSettings(newSettings) {
      try {
        const store = await getStore();
        await store.set('settings', newSettings);
        await store.save();
        this.settings = newSettings;
        return true;
      } catch (err) {
        this.error = '保存设置失败';
        console.error(err);
        return false;
      }
    },
    
    async setTheme(theme) {
      if (this.settings) {
        const updatedSettings = { ...this.settings, theme };
        const success = await this.saveSettings(updatedSettings);
        
        // 使用主题工具函数设置主题
        if (success) {
          setTheme(theme);
        }
        
        return success;
      }
      return false;
    },
    
    async setLanguage(language) {
      if (this.settings) {
        const updatedSettings = { ...this.settings, language };
        return this.saveSettings(updatedSettings);
      }
      return false;
    }
  }
}); 