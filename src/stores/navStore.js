// 导航栏状态管理
import { defineStore } from 'pinia';
import { getNavAPI } from '../apis/apiNav';
import message from '../utils/message';

export const useNavStore = defineStore('nav', {
  state: () => ({
    // 影视分类数据
    categories: [],
    // 缓存不同资源的分类数据
    categoryCache: {},
    // 是否正在加载分类
    loading: false,
    // 当前使用的资源ID
    currentResourceId: null,
    // 当前资源的API地址
    currentResourceApi: null,
    currentResourceName: null,
  }),
  
  actions: {
    // 设置当前资源
    setCurrentResource(resource) {
      if (!resource) return;
      
      this.currentResourceId = resource.id;
      this.currentResourceApi = resource.api;
      this.currentResourceName = resource.name;
      
      // 如果缓存中有当前资源的分类数据，直接使用
      if (this.categoryCache[resource.id]) {
        this.categories = this.categoryCache[resource.id];
        return;
      }
      
      // 否则加载分类数据
      this.fetchCategories();
    },
    
    // 获取影视分类数据
    async fetchCategories() {
      if (!this.currentResourceApi) return;
      
      try {
        this.loading = true;
        const result = await getNavAPI(this.currentResourceApi);
        
        if (result && result.class) {
          // 处理并保存分类数据
          this.categories = result.class;
          
          // 缓存当前资源的分类数据
          if (this.currentResourceId) {
            this.categoryCache[this.currentResourceId] = result.class;
          }
        }
      } catch (error) {
        message.error('获取影视分类失败');
        this.categories = [];
      } finally {
        this.loading = false;
      }
    },
    
    // 清除特定资源的缓存
    clearResourceCache(resourceId) {
      if (resourceId && this.categoryCache[resourceId]) {
        delete this.categoryCache[resourceId];
      }
    },
    
    // 清除所有缓存
    clearAllCache() {
      this.categoryCache = {};
      this.categories = [];
    }
  }
});