import { defineStore } from 'pinia';
import { addHistory, updateHistory, getHistoryByNameYear } from '../db/history';


export const usePlayHistoryStore = defineStore('playHistory', () => {
  // 添加或更新播放记录
  const savePlayRecord = async (record) => {
    const { title,vodYear, source, episode, url, currentTime, movie } = record;
    // 构建数据库记录对象
    const historyRecord = {
      vodName: title,
      vodYear: vodYear,
      source,
      episode,
      videoUrl: url,
      movie: JSON.stringify(movie || {}), // 将movie对象转为JSON字符串
      currentPlaybackTime: currentTime,
      updateAt: Date.now()
    };
    
    try {
      // 查找是否已有该影片的记录
      const existingRecords = await getHistoryByNameYear(title, historyRecord.vodYear);
      
      if (existingRecords && existingRecords.length > 0) {
        // 更新现有记录
        await updateHistory(existingRecords[0].id, historyRecord);
      } else {
        // 添加新记录
        historyRecord.createdAt = Date.now();
        await addHistory(historyRecord);
      }
      
    } catch (e) {
      console.error('保存播放记录失败', e);
    }
  };

  const getPlayRecordByVodNameYear = async (vodName, vodYear) => {
    const records = await getHistoryByNameYear(vodName, vodYear);
    return records;
  };
  
  return {
    savePlayRecord,
    getPlayRecordByVodNameYear,
  };
});