import { defineStore } from 'pinia';

export const useHomeStore = defineStore('home', {
  state: () => ({
    // Cache movie lists by category and search query
    movieListCache: {},
    // Cache search results
    searchResultsCache: {},
    // Remember scroll position
    scrollPosition: 0,
    // Remember current page for each category/search
    currentPages: {},
    // Last updated timestamps for cache invalidation
    lastUpdated: {},
    // Cache time-to-live in milliseconds (10 minutes)
    cacheTTL: 10 * 60 * 1000,
  }),

  actions: {
    // Save movie list to cache
    cacheMovieList(key, movieList, page = 1) {
      // Create a unique key based on category and search query
      if (!this.movieListCache[key]) {
        this.movieListCache[key] = [];
        this.currentPages[key] = 1;
      }

      if (page === 1) {
        // Replace existing data for page 1
        this.movieListCache[key] = [...movieList];
      } else {
        // Append data for subsequent pages
        this.movieListCache[key] = [...this.movieListCache[key], ...movieList];
      }

      // Update current page and timestamp
      this.currentPages[key] = page;
      this.lastUpdated[key] = Date.now();
    },

    // Get cached movie list
    getCachedMovieList(key) {
      // Check if cache exists and is still valid
      if (this.movieListCache[key] && this.lastUpdated[key]) {
        const cacheAge = Date.now() - this.lastUpdated[key];
        if (cacheAge < this.cacheTTL) {
          return {
            data: this.movieListCache[key],
            page: this.currentPages[key] || 1
          };
        }
      }
      return null;
    },

    // Save search results to cache
    cacheSearchResults(query, results) {
      this.searchResultsCache[query] = results;
      this.lastUpdated[`search_${query}`] = Date.now();
    },

    // Get cached search results
    getCachedSearchResults(query) {
      if (this.searchResultsCache[query] && this.lastUpdated[`search_${query}`]) {
        const cacheAge = Date.now() - this.lastUpdated[`search_${query}`];
        if (cacheAge < this.cacheTTL) {
          return this.searchResultsCache[query];
        }
      }
      return null;
    },

    // Save scroll position
    saveScrollPosition(position) {
      this.scrollPosition = position;
    },

    // Get scroll position
    getScrollPosition() {
      return this.scrollPosition;
    },

    // Clear all caches
    clearCache() {
      this.movieListCache = {};
      this.searchResultsCache = {};
      this.scrollPosition = 0;
      this.currentPages = {};
      this.lastUpdated = {};
    },

    // Clear specific cache by key
    clearCacheByKey(key) {
      if (this.movieListCache[key]) {
        delete this.movieListCache[key];
        delete this.currentPages[key];
        delete this.lastUpdated[key];
      }
    }
  }
});