import { load } from '@tauri-apps/plugin-store';

const RESOURCES_FILE = 'settings.json';
let storeInstance = null;

const getStore = async () => {
  if (!storeInstance) {
    storeInstance = await load(RESOURCES_FILE, { autoSave: false });
  }
  return storeInstance;
};

export async function getResources() {
  try {
    const store = await getStore();
    let resources = await store.get('resources');
    return resources;
  } catch (error) {
    console.error('Failed to get resources:', error);
    return null;
  }
}

