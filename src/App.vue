<script setup>
import { computed, onMounted, watch, ref } from "vue";
import { useSettingsStore } from "./stores/settingsStore";
import { useNavStore } from "./stores/navStore";
import { getDefaultResource } from "./db/resources";

const settingsStore = useSettingsStore();
const navStore = useNavStore();

// 设置状态管理
const theme = computed(() => settingsStore.theme);
const isLoading = ref(true);
const hasError = ref(false);

// 应用主题
const applyTheme = (themeName) => {
  if (themeName === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

// 初始化应用
onMounted(async () => {
  try {
    isLoading.value = true;
    
    // 加载设置
    await settingsStore.fetchSettings();
    applyTheme(theme.value);
    
    // 加载默认资源并初始化导航
    const defaultResource = await getDefaultResource();
    if (defaultResource) {
      navStore.setCurrentResource(defaultResource);
    }
  } catch (error) {
    console.error('Failed to initialize app:', error);
    hasError.value = true;
  } finally {
    isLoading.value = false;
  }
});

// 监听主题变化
watch(theme, (newTheme) => {
  applyTheme(newTheme);
});

</script>

<template>
  <div id="app">
    <div v-if="isLoading" class="loading-container">
      <p>加载中...</p>
    </div>
    <div v-else-if="hasError" class="error-container">
      <p>加载失败，请刷新页面重试</p>
    </div>
    <router-view v-else />
  </div>
</template>

<style>
/* 主题相关样式已移至独立CSS文件 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
}
</style>

