import { fetch } from '@tauri-apps/plugin-http';
import { urlJoin } from '../utils/utils';


export const getNavAPI = async (base_url) => {
  const url = urlJoin(base_url, 'api.php/provide/vod/');
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    },
    connectTimeout: 10000,
  },);
  console.log(response);
  return response.json();
};
