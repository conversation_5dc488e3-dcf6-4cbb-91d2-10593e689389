import { fetch } from '@tauri-apps/plugin-http';
import { urlJoin, getPlayUrls } from '../utils/utils';

/**
 * 获取电影列表
 * @param {*} base_url 
 * @param {*} params 
 * @param {string} params.ac list or listVideo
 * @param {number} params.pg 页码
 * @param {string} params.wd 搜索关键词 
 * @returns 
 */
export const getMovieListAPI = async (base_url, params) => {
    const url = urlJoin(base_url, `api.php/provide/vod/?${new URLSearchParams(params).toString()}`);    
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    };    
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: headers,
            connectTimeout: 10000,
          },);
        return response.json();
    } catch (error) {
        console.error(error);
        return [];
    }
  };

  export const getMovieDetailAPI = async (base_url, params) => {
    const url = urlJoin(base_url, `api.php/provide/vod/?${new URLSearchParams(params).toString()}`);    
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    };    
    const response = await fetch(url, {
        method: 'GET',
        headers: headers,
        connectTimeout: 10000,
      },);
    return response.json();
  }
  

  const getSearchAPI = async (base_url, wd, page) => {
    const url = urlJoin(base_url, `api.php/provide/vod/?ac=videolist&wd=${wd}&pg=${page}`);  
    console.log(url);
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    };
    try {
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
        connectTimeout: 5000,
      },);
      if (response.status !== 200) {
        return [];
      }
      return response.json();
    } catch (error) {
      console.error(error);
      return [];
    }
  }

/**
 * 递归获取所有搜索结果并聚合到一个结构中
 * @param {string} base_url 基础URL
 * @param {string} wd 搜索关键词
 * @param {number} startPage 起始页码
 * @param {Array} aggregatedList 已聚合的结果列表
 * @returns {Promise<Object>} 聚合后的结果对象
 */
export const getAllSearchResults = async (base_url, wd, startPage = 1, aggregatedList = [], sourceName='') => {
  try {
    const result = await getSearchAPI(base_url, wd, startPage);
    
    if (result && result.code === 1 && result.list && result.list.length > 0) {
      // 将当前页的结果添加到聚合列表中
      const temList = result.list.map(item => ({
        ...item,
        resources: [{
          sourceName: sourceName,
          episodes: getPlayUrls(item.vod_play_url)
        }],
      }));
      aggregatedList = [...aggregatedList, ...temList];
      
      // 如果当前页小于总页数，则递归获取下一页
      if (startPage < result.pagecount && startPage < 5) {
        return getAllSearchResults(base_url, wd, startPage + 1, aggregatedList, sourceName);
      }
    }
    
    // 返回聚合后的结果
    return {
      code: 1,
      msg: "聚合数据列表",
      total: aggregatedList.length,
      list: aggregatedList
    };
  } catch (error) {
    console.log(base_url);
    console.error("获取所有搜索结果失败:", error);
    return {
      code: 0,
      msg: "获取数据失败",
      total: aggregatedList.length,
      list: aggregatedList
    };
  }
};

  // 聚合搜索
export const getAggregationSearchAPI = async (resourceList, wd) => {
  try {
    // 创建Promise数组，每个资源创建一个Promise
    const promises = resourceList.map(async (resource) => {
      const sourceName = resource.name;
      const base_url = resource.api;
      try {
        const result = await getAllSearchResults(base_url, wd, 1, [], sourceName);
        return  result.list;
      } catch (error) {
        console.error(`获取资源 ${sourceName} 失败:`, error);
        return [];
      }
    });

    // 并发执行所有请求
    const resources = await Promise.all(promises);
    return resources.flat();
  } catch (error) {
    console.error("聚合搜索失败:", error);
    return [];
  }
}

// 资源合并，对getAggregationSearchAPI的结果，把vod_name值想的，把里面的resource合并为一起，返回列表

export const mergeResource = (resourceList) => {
  const temMap = new Map();
  for (const res of resourceList) {
    const key_name = `${res.vod_name}_${res.vod_year || ''}`;
    if (temMap.has(key_name)) {
      temMap.get(key_name).resources.push(...res.resources);
    } else {
      temMap.set(key_name, res);
    }
  }
  return Array.from(temMap.values());
}
