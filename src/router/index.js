import { createRouter, createWebHashHistory } from 'vue-router';
import { useSettingsStore } from '../stores/settingsStore';

const routes = [
  {
    path: '/setup',
    name: 'Setup',
    component: () => import('../views/Setup.vue'),
  },
  {
    path: '/',
    name: 'MainLayout',
    component: () => import('../views/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('../views/Home/index.vue'),
        meta: { keepAlive: true } // Add keepAlive flag to preserve component state
      },
      {
        path: 'movie/:id',
        name: 'MovieDetail',
        component: () => import('../views/MovieDetail.vue'),
        props: true,
        meta: { keepAlive: false } // Don't keep this page alive
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('../views/Settings.vue'),
        props: true
      },
      {
        path: 'play/:id',
        name: 'Player',
        component: () => import('../views/Player.vue'),
        props: true
      },
      {
        path: 'history',
        name: 'History',
        component: () => import('../views/History.vue'),
        props: true
      },
    ]
  },
  {
    path: '/error',
    name: 'Error',
    component: () => import('../views/Error.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: { name: 'Home' }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 全局路由守卫，检查是否已完成初始化设置
router.beforeEach(async (to, from, next) => {
  // 如果目标路由是设置页面或错误页面，则直接进入
  if (to.name === 'Setup' || to.name === 'Error') {
    return next();
  }
  
  try {
    const settingsStore = useSettingsStore();
    await settingsStore.fetchSettings();
    
    // 如果未初始化，则重定向到设置页面
    if (!settingsStore.isInitialized) {
      return next({ name: 'Setup' });
    }
    
    next();
  } catch (error) {
    console.error('Router guard error:', error);
    // 发生错误时重定向到错误页面
    return next({ name: 'Error', params: { message: '设置加载失败' } });
  }
});

export default router;
