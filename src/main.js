import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// 导入Element Plus暗色模式CSS变量
import 'element-plus/theme-chalk/dark/css-vars.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import App from "./App.vue";
import router from "./router";

// 导入主题和样式
import './styles/variables.css';
import './styles/components.css';
import './styles/common.css';

// 导入主题工具函数
import { initTheme, watchSystemTheme } from './utils/themeUtils';

// 初始化主题
initTheme();

// 监听系统主题变化
watchSystemTheme();

const app = createApp(App);
const pinia = createPinia();

// Register all icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(pinia);
app.use(router);
app.use(ElementPlus);
app.mount("#app");
