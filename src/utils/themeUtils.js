/**
 * 主题切换工具函数
 */

/**
 * 设置主题
 * @param {string} theme - 'light' 或 'dark'
 */
export function setTheme(theme) {
  const html = document.documentElement;
  
  if (theme === 'dark') {
    html.classList.add('dark');
  } else {
    html.classList.remove('dark');
  }
  
  // 保存到本地存储
  localStorage.setItem('theme', theme);
}

/**
 * 获取当前主题
 * @returns {string} 'light' 或 'dark'
 */
export function getTheme() {
  // 优先从本地存储获取
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    return savedTheme;
  }
  
  // 检查系统偏好
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
}

/**
 * 初始化主题
 */
export function initTheme() {
  const theme = getTheme();
  setTheme(theme);
  return theme;
}

/**
 * 切换主题
 * @returns {string} 新的主题
 */
export function toggleTheme() {
  const currentTheme = getTheme();
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
  setTheme(newTheme);
  return newTheme;
}

/**
 * 监听系统主题变化
 */
export function watchSystemTheme() {
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    mediaQuery.addEventListener('change', (e) => {
      // 只有在没有手动设置主题时才跟随系统
      if (!localStorage.getItem('theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        setTheme(newTheme);
      }
    });
  }
} 