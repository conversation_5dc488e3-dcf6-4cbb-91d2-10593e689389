// 实现一个url拼接函数，如果url以/结尾，则直接拼接，否则在url后面拼接/
export const urlJoin = (url, path) => {
  if (url.endsWith('/')) {
    return url + path;
  }
  return url + '/' + path;
};

/**
 * 解析vod_play_url，返回m3u8链接和对应的标题
 * @param {string} vodPlayUrl - 播放地址字符串
 * @returns {Array<{title: string, url: string}>} - 标题和m3u8链接列表
 */
export const getPlayUrls = (vodPlayUrl) => {
  if (!vodPlayUrl) return [];
  
  const result = [];
  
  // 使用正则表达式匹配所有的标题和m3u8链接
  // 格式: 标题$URL，其中URL包含.m3u8
  const regex = /([^$#]+)\$(https?:\/\/[^$#]+\.m3u8[^$#]*)/g;
  let match;
  
  while ((match = regex.exec(vodPlayUrl)) !== null) {
    const title = match[1];
    const url = match[2];
    
    result.push({
      name: title,
      url: url
    });
  }
  
  return result;
};

// vod_play_url: "正片$https://vv.jisuzyv.com/play/PdRK24ze$$$正片$https://vv.jisuzyv.com/play/PdRK24ze/index.m3u8", 输入vod_play_url，获取正片播放地址
// vod_play_url: "第01集$https://cdn.wlcdn88.com:777/ac482e1f/index.m3u8#第02集$https://cdn.wlcdn88.com:777/1b308514/index.m3u8#第03集$https://cdn.wlcdn88.com:777/c65a8b65/index.m3u8#第04集$https://cdn.wlcdn88.com:777/223af566/index.m3u8#第05集$https://cdn.wlcdn88.com:777/43098a51/index.m3u8#第06集$https://cdn.wlcdn88.com:777/9a6bfd77/index.m3u8"
