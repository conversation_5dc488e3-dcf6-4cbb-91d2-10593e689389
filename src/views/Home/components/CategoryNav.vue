<template>
    <div class="category-nav">
        <div class="category-container">
            <!-- 骨架屏加载状态 -->
            <template v-if="navStore.loading">
                <div v-for="i in 10" :key="`skeleton-${i}`" class="category-skeleton">
                    <el-skeleton-item variant="text" style="width: 60px; height: 32px; border-radius: 20px;" />
                </div>
            </template>
            <!-- 实际分类数据 -->
            <template v-else>
                <!-- 全部分类 -->
                <el-tag class="category-item" :class="{ active: activeCategory === 0 }" @click="selectCategory(0)">全部</el-tag>
                
                <!-- 一级分类 -->
                <el-tag v-for="category in parentCategories" :key="category.type_id" class="category-item"
                    :class="{ active: activeCategory === category.type_id }" @click="activeChildCategory(category.type_id)">
                    {{ category.type_name }}
                </el-tag>
                
                <!-- 二级分类 - 在选择了父分类时显示对应子类，或在全部时显示所有子类 -->
                <template v-if="(activeParentCategory && childCategories.length > 0) || (activeCategory === 0 && allChildCategories.length > 0)">
                    <div class="subcategory-divider"></div>
                    <el-tag v-for="child in activeCategory === 0 ? allChildCategories : childCategories" 
                        :key="child.type_id" 
                        class="category-item subcategory"
                        :class="{ active: activeCategory === child.type_id }" 
                        @click="selectCategory(child.type_id)">
                        {{ child.type_name }}
                    </el-tag>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useNavStore } from '../../../stores/navStore';

const activeCategory = ref(0); // Default active category
const emit = defineEmits(['categorySelected']);

// 使用导航栏状态管理
const navStore = useNavStore();

// 计算一级分类（type_pid为0或不存在type_pid的分类）
const parentCategories = computed(() => {
    if (!navStore.categories || navStore.categories.length === 0) return [];
    
    // 检查是否有包含type_pid的分类
    const hasTypePid = navStore.categories.some(cat => 'type_pid' in cat);
    
    if (hasTypePid) {
        // 如果有type_pid字段，返回所有type_pid为0的分类作为一级分类
        return navStore.categories.filter(cat => cat.type_pid === 0);
    } else {
        // 如果没有type_pid字段，所有分类都是一级分类
        return navStore.categories;
    }
});

// 当前选中的父分类ID
const activeParentCategory = computed(() => {
    if (activeCategory.value === 0) return null;
    
    // 检查当前选中的是否为父分类
    const isParent = parentCategories.value.some(cat => cat.type_id === activeCategory.value);
    if (isParent) return activeCategory.value;
    
    // 如果是子分类，查找其父分类
    const selectedCategory = navStore.categories.find(cat => cat.type_id === activeCategory.value);
    if (selectedCategory && 'type_pid' in selectedCategory && selectedCategory.type_pid !== 0) {
        return selectedCategory.type_pid;
    }
    
    return null;
});

// 计算二级分类（基于当前选中的父分类）
const childCategories = computed(() => {
    if (!activeParentCategory.value || !navStore.categories || navStore.categories.length === 0) return [];
    
    // 检查是否有包含type_pid的分类
    const hasTypePid = navStore.categories.some(cat => 'type_pid' in cat);
    
    if (hasTypePid) {
        // 返回type_pid等于当前选中父分类ID的所有分类
        return navStore.categories.filter(cat => 
            'type_pid' in cat && cat.type_pid === activeParentCategory.value
        );
    }
    
    return [];
});

// 计算所有子分类（用于"全部"分类时显示）
const allChildCategories = computed(() => {
    if (!navStore.categories || navStore.categories.length === 0) return [];
    
    // 检查是否有包含type_pid的分类
    const hasTypePid = navStore.categories.some(cat => 'type_pid' in cat);
    
    if (hasTypePid) {
        // 返回所有非零type_pid的分类作为子分类
        return navStore.categories.filter(cat => 'type_pid' in cat && cat.type_pid !== 0);
    }
    
    return [];
});

const activeChildCategory = (typeId) => {
    activeCategory.value = typeId;
    emit('categorySelected', typeId);
};

// Select category and emit to parent
const selectCategory = (typeId) => {
    activeCategory.value = typeId;
    emit('categorySelected', typeId);
};

// Reset category to All (0)
const resetCategory = () => {
    activeCategory.value = 0;
};

// Expose method to parent component
defineExpose({
    resetCategory
});

// 当资源变更时，监听当前资源ID变化
watch(() => navStore.currentResourceId, (newId, oldId) => {
    if (newId !== oldId) {
        resetCategory();
        emit('categorySelected', 0);
    }
}, { deep: true });

</script>

<style scoped>
.category-nav {
    width: 100%;
    padding: 16px 0;
    background-color: var(--el-bg-color);
}

.category-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 12px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

.category-item {
    padding: 1px 6px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    background-color: var(--el-bg-color);
    color: var(--el-text-color-regular);
}

.category-item:hover {
    color: var(--el-color-primary);
    transform: translateY(-2px);
}

.category-item.active {
    color: var(--el-color-primary);
    font-weight: 500;
}

/* 骨架屏样式 */
.category-skeleton {
    padding: 8px 16px;
}

/* 二级分类样式 */
.subcategory-divider {
    width: 100%;
    height: 1px;
    margin: 8px 0;
    background-color: var(--el-border-color-lighter);
}

.subcategory {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color-lighter);
}

@media (max-width: 768px) {
    .category-container {
        gap: 8px;
    }

    .category-item {
        padding: 6px 12px;
        font-size: 12px;
    }
}
</style>
