<template>
  <div class="home-container">
    <el-row :gutter="20" class="search-section">
      <el-col :span="24">
        <div class="search-container">
          <div class="search-input-group">
            <div class="custom-search-input">
              <el-input 
                v-model="searchQuery" 
                placeholder="电影、剧集、综艺、动漫......" 
                class="search-input" 
                clearable
                @keyup.enter="handleSearch"
              >
                <template #suffix>
                  <el-button 
                    class="search-btn" 
                    circle 
                    @click="handleSearch"
                  >
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
            <div class="aggregation-search">
              <span class="aggregation-label">聚合搜索</span>
              <el-switch v-model="isAggregationSearch" />
            </div>
            <div class="refresh-btn">
              <el-tooltip content="刷新数据" placement="top">
                <el-button 
                  circle 
                  size="small"
                  @click="refreshData"
                >
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <CategoryNav ref="categoryNavRef" @categorySelected="handleCategorySelected" />

    <!-- 电影列表 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <div v-if="loading && currentPage === 1" class="movie-list">
          <!-- 骨架屏加载状态 -->
          <div v-for="i in 12" :key="i" class="movie-skeleton">
            <el-skeleton animated>
              <template #template>
                <div class="movie-card">
                  <div class="movie-poster skeleton-poster"></div>
                  <div class="movie-info">
                    <el-skeleton-item variant="text" style="width: 80%; height: 16px; margin-bottom: 8px;" />
                    <el-skeleton-item variant="text" style="width: 40%; height: 12px;" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>
        </div>
        <div v-else class="movie-list">
          <MovieCard v-for="movie in movieList" :key="movie.vod_id" :movie="movie" @click="viewMovieDetail(movie)" />
        </div>
        
        <!-- 加载更多指示器 -->
        <div v-if="loading && currentPage > 1" class="loading-more">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
        
        <!-- 无限滚动观察元素 -->
        <div ref="loadMoreTrigger" class="load-more-trigger"></div>
        
        <!-- 无数据提示 -->
        <el-empty v-if="!loading && movieList.length === 0" description="暂无数据" />
      </el-col>
    </el-row>
    
    <!-- 返回顶部按钮 -->
    <el-backtop :right="20" :bottom="20" target=".content"></el-backtop>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Search, Top, Loading, Refresh } from '@element-plus/icons-vue';
import CategoryNav from './components/CategoryNav.vue';
import { getMovieListAPI, getAggregationSearchAPI, mergeResource } from '../../apis/apiMovies';
import { getDefaultResource, getResourceList } from '../../db/resources';
import { useNavStore } from '../../stores/navStore';
import { useMovieStore } from '../../stores/movieStore';
import { useHomeStore } from '../../stores/homeStore';
import MovieCard from '../../components/MovieCard.vue';
import message from '../../utils/message'
import { getPlayUrls } from '../../utils/utils';

const router = useRouter();
const navStore = useNavStore();
const movieStore = useMovieStore();
const homeStore = useHomeStore();
const searchQuery = ref('');
const movieList = ref([]);
const loading = ref(true);
const currentPage = ref(1);
const hasMoreData = ref(true);
const loadMoreTrigger = ref(null);
const currentCategoryId = ref(null);
const observer = ref(null);
const categoryNavRef = ref(null);  // 添加对CategoryNav组件的引用
const isAggregationSearch = ref(true);

const getResource = async () => {
  try {
    const resource = await getDefaultResource();
    if (resource) {
      navStore.setCurrentResource(resource);
    }
  } catch (error) {
    console.error(error);
  }
  
};

const getMovieList = async (categoryId, page = 1, isLoadMore = false) => {
  // Generate a cache key based on category ID and resource
  const cacheKey = `cat_${categoryId || 0}_${isAggregationSearch.value ? 'agg' : 'normal'}_res_${searchQuery.value}_${navStore.currentResourceId}`;
  
  // Check if we have cached data and it's not a load more request
  if (!isLoadMore && page === 1) {
    const cachedData = homeStore.getCachedMovieList(cacheKey);
    if (cachedData) {
      movieList.value = cachedData.data;
      currentPage.value = cachedData.page;
      loading.value = false;
      return cachedData.data;
    }
  }

  if (!isLoadMore) {
    loading.value = true;
  }
  
  if (!navStore.currentResourceApi) {
    loading.value = false;
    return [];
  }
  
  const params = { ac: 'detail', pg: page };
  
  if (categoryId) {
    params.t = categoryId;
  }
  
  if (searchQuery.value) {
    params.wd = searchQuery.value;
  }
  
  try {
    const res = await getMovieListAPI(navStore.currentResourceApi, params);
    if (!res.list) {
      loading.value = false;
      return [];
    }
    const temMovieList = res.list.map(item => ({
      ...item,
      resources: [{
        sourceName: navStore.currentResourceName,
        episodes: getPlayUrls(item.vod_play_url)
      }],
    }));
    if (isLoadMore) {
      // 追加数据
      movieList.value = [...movieList.value, ...(temMovieList || [])];
      // Cache the updated list
      homeStore.cacheMovieList(cacheKey, movieList.value, page);
    } else {
      // 替换数据
      movieList.value = temMovieList || [];
      // Cache the new list
      homeStore.cacheMovieList(cacheKey, movieList.value, page);
    }
    
    // 判断是否还有更多数据
    // 检查是否有更多数据 - 通过判断返回的数据量是否达到预期的一页数据量
    // 如果返回的数据少于预期（例如每页20条），说明已经没有更多数据了
    const pageSize = 20; // 假设API每页返回20条数据
    hasMoreData.value = temMovieList && temMovieList.length >= pageSize;
    
    return temMovieList || [];
  } catch (error) {
    console.error(error);
    if (!isLoadMore) {
      movieList.value = [];
    }
    hasMoreData.value = false;
    return [];
  } finally {
    loading.value = false;
  }
};

const handleSearch = async () => {
  if (searchQuery.value === '') {
    getMovieList(currentCategoryId.value);
    return;
  }
  loading.value = true;
  // 重置分类到全部(0)
  currentCategoryId.value = 0;
  currentPage.value = 1;
  // 重置CategoryNav组件的激活状态
  if (categoryNavRef.value) {
    categoryNavRef.value.resetCategory();
  }
  
  const searchCacheKey = `search_${isAggregationSearch.value ? 'agg' : 'normal'}_${searchQuery.value}_${navStore.currentResourceId}`;
  const cachedResults = homeStore.getCachedSearchResults(searchCacheKey);
  if (cachedResults) {
    movieList.value = cachedResults;
    loading.value = false;
    return;
  }
  if (isAggregationSearch.value) {
    const resourceList = await getResourceList();
    const result = await getAggregationSearchAPI(resourceList, searchQuery.value);
    if (result.length > 0) {
      const temList = mergeResource(result);
      movieList.value = temList;
      // Cache the search results
      homeStore.cacheSearchResults(searchCacheKey, temList);
    } else {
      message.error('暂无数据');
      movieList.value = [];
      homeStore.cacheSearchResults(searchCacheKey, movieList.value);
    }
  } else {
    await getMovieList(0);
    homeStore.cacheSearchResults(searchCacheKey, movieList.value);
  }
  
  loading.value = false;
};

const loadMore = async () => {
  if (loading.value || !hasMoreData.value) return;
  
  loading.value = true;
  currentPage.value++;
  await getMovieList(currentCategoryId.value, currentPage.value, true);
};

const setupIntersectionObserver = () => {
  // 清除旧的观察者
  if (observer.value) {
    observer.value.disconnect();
  }
  
  // 创建新的观察者
  observer.value = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting && !loading.value && hasMoreData.value) {
      loadMore();
    }
  }, {
    rootMargin: '0px 0px 200px 0px' // 提前200px触发加载
  });
  
  // 开始观察
  nextTick(() => {
    if (loadMoreTrigger.value) {
      observer.value.observe(loadMoreTrigger.value);
    }
  });
};

// 监听资源变更
watch(() => navStore.currentResourceId, () => {
  currentPage.value = 1;
  hasMoreData.value = true;
  getMovieList(currentCategoryId.value);
});

// Refresh data by clearing cache and reloading
const refreshData = () => {
  // Clear cache for current category
  homeStore.clearCache();
  // Reset page and reload data
  currentPage.value = 1;
  if (searchQuery.value && isAggregationSearch.value) {
    handleSearch();
  } else {
    getMovieList(currentCategoryId.value);
  }
  
  message.success('数据已刷新');
};

// Save scroll position when navigating away
const saveScrollPosition = () => {
  const contentElement = document.querySelector('.content');
  if (contentElement) {
    homeStore.saveScrollPosition(contentElement.scrollTop);
  }
};

// Restore scroll position when returning to the page
const restoreScrollPosition = () => {
  nextTick(() => {
    const contentElement = document.querySelector('.content');
    if (contentElement && homeStore.getScrollPosition() > 0) {
      contentElement.scrollTop = homeStore.getScrollPosition();
    }
  });
};

onMounted(async () => {
  await getResource();
  await getMovieList(currentCategoryId.value);
  setupIntersectionObserver();
  restoreScrollPosition();
  
  // Add event listener to save scroll position when leaving the page
  window.addEventListener('beforeunload', saveScrollPosition);
});

onUnmounted(() => {
  // 清除观察者
  if (observer.value) {
    observer.value.disconnect();
  }
  
  // Remove event listener
  window.removeEventListener('beforeunload', saveScrollPosition);
});

const handleCategorySelected = (categoryId) => {
  currentCategoryId.value = categoryId;
  currentPage.value = 1;
  searchQuery.value = ''
  getMovieList(categoryId);
};

// 查看电影详情
const viewMovieDetail = (movie) => {
  saveScrollPosition();
  movieStore.setMovie(movie);
  movieStore.setSources(movie.resources);
  router.push({
    name: 'MovieDetail',
    params: { id: movie.vod_id }
  });
};
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: bold;
}

.welcome-section p {
  font-size: 1.2rem;
  color: var(--el-text-color-secondary);
}

.search-section {
  margin-bottom: 30px;
}

.search-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  width: 100%;
}

.search-input-group {
  display: flex;
  align-items: center;
  max-width: 700px;
  width: 100%;
  position: relative;
}

.custom-search-input {
  flex: 1;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 50px;
  padding-right: 10px;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover),
.search-input :deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.search-input :deep(.el-input__inner) {
  height: 48px;
  font-size: 16px;
  padding-left: 20px;
  color: #333;
}

.search-input :deep(.el-input__suffix) {
  right: 5px;
}

.search-btn {
  background-color: var(--el-color-primary);
  color: white;
  width: 40px;
  height: 40px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.search-btn:hover {
  background-color: var(--el-color-primary-dark-2);
  transform: scale(1.05);
}

.aggregation-search {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.aggregation-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-right: 5px;
}

.refresh-btn {
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.stats-section {
  margin-bottom: 30px;
}

.stat-card {
  height: 100%;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 2.5rem;
  padding: 15px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  margin-right: 15px;
}

.movie-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* 骨架屏样式 */
.movie-skeleton {
  width: 100%;
}

.skeleton-poster {
  width: 100%;
  padding-top: 150%;
  background-color: var(--el-fill-color-light);
  border-radius: 8px 8px 0 0;
}

.movie-card {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.movie-info {
  padding: 12px;
  background-color: #fff;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: var(--el-text-color-secondary);
}

.loading-more span {
  margin-left: 10px;
}

.load-more-trigger {
  height: 1px;
  margin-top: 10px;
}

/* 确保返回顶部按钮可见 */
.el-backtop {
  background-color: var(--el-color-primary) !important;
  color: white !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}
</style>