<template>
  <div class="history-container">    
    <!-- 搜索栏 -->
    <el-row :gutter="20" class="search-section">
      <el-col :span="24">
        <div class="search-container">
          <div class="search-input-group">
            <div class="custom-search-input">
              <el-input 
                v-model="search" 
                placeholder="搜索历史播放记录" 
                class="search-input" 
                clearable
                @keyup.enter="handleSearch"
              >
                <template #suffix>
                  <el-button 
                    class="search-btn" 
                    circle 
                    @click="handleSearch"
                  >
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 历史记录列表 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-empty v-if="history.length === 0 && !loading" description="暂无播放记录" />
        
        <!-- 加载状态 -->
        <div v-if="loading && currentPage === 1" class="history-list">
          <div v-for="i in 12" :key="i" class="history-skeleton">
            <el-skeleton animated>
              <template #template>
                <div class="history-card">
                  <div class="history-poster skeleton-poster"></div>
                  <div class="history-info">
                    <el-skeleton-item variant="text" style="width: 80%; height: 16px; margin-bottom: 8px;" />
                    <el-skeleton-item variant="text" style="width: 40%; height: 12px;" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>
        </div>
        
        <!-- 历史记录项 -->
        <div v-else class="history-list">
          <div v-for="item in history" :key="item.id" class="history-card-wrapper" @click="navigateToPlay(item)">
            <div class="history-card">
              <div class="history-poster">
                <el-image 
                  :src="JSON.parse(item.movie).vod_pic" 
                  :alt="JSON.parse(item.movie).vod_name"
                  loading="lazy"
                  fit="cover">
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="history-overlay">
                  <div class="history-play-icon">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </div>
                <span class="history-episode">{{ item.episode || '未知剧集' }}</span>
                <span class="history-progress">{{ formatTime(item.currentPlaybackTime) }}</span>
              </div>
              <div class="history-info">
                <h3 class="history-title">{{ JSON.parse(item.movie).vod_name }}</h3>
                <div class="history-meta">
                  <span class="source-tag">{{ item.source }}</span>
                  <span class="time-tag">{{ formatDate(item.updateAt) }}</span>
                </div>
              </div>
              <div class="history-actions" @click.stop>
                <el-button 
                  type="danger" 
                  circle 
                  size="small" 
                  @click="handleDelete(item.id)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      
      </el-col>
    </el-row>
    
    <!-- 加载更多指示器 -->
    <div v-if="loading && currentPage > 1" class="loading-more">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    
    <!-- 无限滚动触发器 -->
    <div ref="loadMoreTrigger" class="load-more-trigger"></div>
    
    <!-- 返回顶部按钮 -->
    <el-backtop :right="20" :bottom="20" target=".content"></el-backtop>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { Search, Delete, Loading, Picture, VideoPlay } from '@element-plus/icons-vue';
import { getHistory, deleteHistory } from '../db/history';
import { ElMessageBox } from 'element-plus';
import message from '../utils/message';

const router = useRouter();
const history = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const search = ref('');
const loading = ref(false);
const hasMoreData = ref(true);
const loadMoreTrigger = ref(null);
const observer = ref(null);

// 加载历史记录
const loadHistory = async (isLoadMore = false) => {
  if (loading.value) return;
  
  loading.value = true;
  
  try {
    const offset = isLoadMore ? history.value.length : 0;
    const result = await getHistory(search.value, offset, pageSize.value);
    
    if (isLoadMore) {
      history.value = [...history.value, ...result];
    } else {
      history.value = result;
    }
    
    // 判断是否还有更多数据
    hasMoreData.value = result.length === pageSize.value;
  } catch (error) {
    console.error('Failed to load history:', error);
    message.error('加载历史记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  history.value = [];
  hasMoreData.value = true;
  loadHistory();
};

// 处理删除
const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这条播放记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteHistory(id);
    history.value = history.value.filter(item => item.id !== id);
    message.success('删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete history:', error);
      message.error('删除失败');
    }
  }
};

// 跳转到播放页面
const navigateToPlay = (item) => {
  const movieData = JSON.parse(item.movie);
  router.push({
    path: `/play/${movieData.vod_id}`,
    query: { 
      title: movieData.vod_name,
      source: item.source,
      episode: item.episode,
      url: item.videoUrl,
      movie: item.movie
    }
  });
};

// 设置无限滚动
const setupIntersectionObserver = () => {
  // 清除旧的观察者
  if (observer.value) {
    observer.value.disconnect();
  }
  
  // 创建新的观察者
  observer.value = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting && !loading.value && hasMoreData.value) {
      currentPage.value++;
      loadHistory(true);
    }
  }, {
    rootMargin: '0px 0px 200px 0px' // 提前200px触发加载
  });
  
  // 开始观察
  nextTick(() => {
    if (loadMoreTrigger.value) {
      observer.value.observe(loadMoreTrigger.value);
    }
  });
};

// 格式化日期
const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 格式化时间（秒转为分:秒）
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${String(remainingSeconds).padStart(2, '0')}`;
};

onMounted(() => {
  loadHistory();
  setupIntersectionObserver();
});

onUnmounted(() => {
  // 清除观察者
  if (observer.value) {
    observer.value.disconnect();
  }
});
</script>

<style scoped>
.history-container {
  padding: 20px;
}

.history-container h1 {
  margin-bottom: 20px;
  font-size: 1.8rem;
  font-weight: bold;
}

/* 搜索栏样式 */
.search-section {
  margin-bottom: 30px;
}

.search-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  width: 100%;
}

.search-input-group {
  display: flex;
  align-items: center;
  max-width: 700px;
  width: 100%;
  position: relative;
}

.custom-search-input {
  flex: 1;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 50px;
  padding-right: 10px;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover),
.search-input :deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.search-input :deep(.el-input__inner) {
  height: 48px;
  font-size: 16px;
  padding-left: 20px;
  color: #333;
}

.search-input :deep(.el-input__suffix) {
  right: 5px;
}

.search-btn {
  background-color: var(--el-color-primary);
  color: white;
  width: 40px;
  height: 40px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.search-btn:hover {
  background-color: var(--el-color-primary-dark-2);
  transform: scale(1.05);
}

/* 历史记录列表样式 */
.history-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.history-card-wrapper {
  position: relative;
  width: 100%;
}

.history-card {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: #fff;
}

.history-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.history-poster {
  position: relative;
  width: 100%;
  padding-top: 150%; /* 2:3 宽高比 */
  overflow: hidden;
}

.history-poster :deep(.el-image) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.history-poster :deep(.el-image__inner) {
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.history-card:hover .history-poster :deep(.el-image__inner) {
  transform: scale(1.05);
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

.image-error .el-icon {
  font-size: 32px;
}

.history-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease;
}

.history-card:hover .history-overlay {
  opacity: 1;
}

.history-play-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.history-play-icon .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.history-info {
  padding: 12px;
  background: #fff;
}

.history-title {
  margin: 0 0 5px 0;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.source-tag, .time-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary-light-7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.time-tag {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border: 1px solid var(--el-color-info-light-7);
}

.history-episode {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(130, 127, 127, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2;
}

.history-progress {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
  z-index: 2;
}

.history-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.history-card-wrapper:hover .history-actions {
  opacity: 1;
}

/* 骨架屏样式 */
.history-skeleton {
  width: 100%;
}

.skeleton-poster {
  width: 100%;
  padding-top: 150%;
  background-color: var(--el-fill-color-light);
  border-radius: 8px 8px 0 0;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: var(--el-text-color-secondary);
}

.loading-more span {
  margin-left: 10px;
}

.load-more-trigger {
  height: 1px;
  margin-top: 10px;
}

/* 确保返回顶部按钮可见 */
.el-backtop {
  background-color: var(--el-color-primary) !important;
  color: white !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}
</style>