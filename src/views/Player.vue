<template>
  <div class="player-page">
    <!-- 标题栏 -->
    <div class="header-bar">
      <div class="back-button" @click="goBack">
        <el-icon>
          <ArrowLeft />
        </el-icon>
      </div>
      <div class="title-info">
        <h2 class="video-title">{{ title }}</h2>
        <p class="video-meta" v-if="currentEpisode">
          {{ currentSource }} | {{ currentEpisode }}
        </p>
      </div>
    </div>

    <!-- 播放器主内容区域 -->
    <div class="player-container">
      <div class="video-info">
      </div>
      <div class="player-wrapper-container">
        <div class="player-wrapper" ref="playerWrapper">
          <div ref="artPlayerRef" class="art-player"></div>
        </div>
      </div>

    </div>

    <!-- 使用Drawer作为选集列表 -->
    <el-drawer v-model="showEpisodesList" title="剧集选择" direction="rtl" size="350px" :with-header="true"
      :close-on-press-escape="true">
      <template #header>
        <div class="drawer-header">
          <div class="tab-title" :class="{ active: activeTab === 'episodes' }" @click="activeTab = 'episodes'">选集</div>
          <div class="tab-title" :class="{ active: activeTab === 'sources' }" @click="activeTab = 'sources'">换源</div>
        </div>
      </template>

      <div class="drawer-content">
        <!-- 来源列表 -->
        <div class="source-container" v-show="activeTab === 'sources'">
          <div class="source-item" v-for="source in sources" :key="source.sourceName"
            :class="{ active: source.sourceName === currentSource }" @click="currentSource = source.sourceName">
            <div class="source-name">{{ source.sourceName }}</div>
            <div class="episode-count">共 {{ source.episodes.length }} 集</div>
          </div>
        </div>

        <!-- 选集网格 -->
        <div class="episodes-grid" v-show="activeTab === 'episodes'">
          <div class="page-indicator">
            <span v-if="totalPages > 1">{{ currentPageStart }}-{{ currentPageEnd }} / {{ currentSourceEpisodes.length
              }}</span>
            <span v-else>共 {{ currentSourceEpisodes.length }} 集</span>
            <div class="pagination-controls" v-if="totalPages > 1">
              <el-button size="small" :disabled="currentPage === 1" @click="currentPage--">上一页</el-button>
              <span class="page-number">{{ currentPage }}/{{ totalPages }}</span>
              <el-button size="small" :disabled="currentPage === totalPages" @click="currentPage++">下一页</el-button>
            </div>
          </div>
          <div class="episodes-wrapper">
            <div v-for="episode in paginatedEpisodes" :key="episode.name" class="episode-item"
              :class="{ active: paginatedEpisodes.indexOf(episode) + (currentPage - 1) * pageSize === currentEpisodeIndex }"
              @click="playEpisode(episode.name, episode.url)">
              {{ episode.name }}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Artplayer from 'artplayer';
import Hls from 'hls.js';
import { ArrowLeft } from '@element-plus/icons-vue';
import { usePlayHistoryStore } from '../stores/playHistoryStore';
import { ElMessageBox } from 'element-plus';
import { getCurrentWindow } from '@tauri-apps/api/window';
const appWindow = getCurrentWindow();


// 从剧集名称中提取数字，用于跨源匹配相同集数
const extractEpisodeNumber = (episodeName) => {
  if (!episodeName) return null;

  // 尝试提取纯数字部分（匹配常见格式：第1集，01，EP01，第一季第1集等）
  const numberMatch = episodeName.match(/(\d+)/);
  if (numberMatch && numberMatch[1]) {
    return parseInt(numberMatch[1], 10);
  }

  // 如果没有找到数字，返回null
  return null;
};

// 路由和状态
const route = useRoute();
const router = useRouter();
const playHistoryStore = usePlayHistoryStore();


// 播放器相关参数
const artPlayerRef = ref(null);
const playerWrapper = ref(null);  // 新增对播放器包装元素的引用
const artPlayer = ref(null);
const currentTime = ref(0);
const autoPlayNext = ref(localStorage.getItem('autoPlayNext') === 'true' || false);
const autoFullscreen = ref(localStorage.getItem('autoFullscreen') === 'true' || false);

// 跳过片头，和 跳过的时间
const skipIntro = ref(localStorage.getItem('skipIntro') === 'true' || false);
const skipTime = ref(parseInt(localStorage.getItem('skipTime')) || 90);
// 跳过片尾，和 跳过的时间
const skipOutro = ref(localStorage.getItem('skipOutro') === 'true' || false);
const skipOutroTime = ref(parseInt(localStorage.getItem('skipOutroTime')) || 60);

// 视频信息
const id = ref(route.params.id);
const title = ref(route.query.title || '未知影片');
const currentSource = ref(route.query.source || '');
const currentEpisode = ref(route.query.episode || '');
const currentUrl = ref(route.query.url || '');
const movie = ref(JSON.parse(route.query.movie || '{}'));

// 选集相关参数
const sources = ref(JSON.parse(JSON.stringify(movie.value.resources) || '[]'));
const showEpisodesList = ref(true);
const activeTab = ref('episodes'); // 默认显示选集标签页

// 分页相关参数
const pageSize = 50; // 每页显示50集
const currentPage = ref(1);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(currentSourceEpisodes.value.length / pageSize);
});

// 计算当前页的开始和结束位置
const currentPageStart = computed(() => {
  return (currentPage.value - 1) * pageSize + 1;
});

const currentPageEnd = computed(() => {
  return Math.min(currentPage.value * pageSize, currentSourceEpisodes.value.length);
});

// 计算当前页的剧集
const paginatedEpisodes = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  const end = start + pageSize;
  return currentSourceEpisodes.value.slice(start, end);
});

// 计算当前源的所有剧集
const currentSourceEpisodes = computed(() => {
  const source = sources.value.find(s => s.sourceName === currentSource.value);
  return source ? source.episodes : [];
});

// 计算是否有上一集、下一集
const currentEpisodeIndex = ref(0);

const hasPreviousEpisode = computed(() => {
  return currentEpisodeIndex.value > 0;
});

const hasNextEpisode = computed(() => {
  return currentEpisodeIndex.value < currentSourceEpisodes.value.length - 1 && currentEpisodeIndex.value !== -1;
});

// 切换显示/隐藏选集列表
const toggleEpisodesList = () => {
  showEpisodesList.value = !showEpisodesList.value;
};

// 安全地切换选集列表，处理全屏状态
const safeToggleEpisodesList = () => {
  if (artPlayer.value) {
    // 如果在全屏模式，先退出全屏
    if (artPlayer.value.fullscreen) {
      artPlayer.value.fullscreen = false;
      setTimeout(() => {
        showEpisodesList.value = true;
      }, 300);
    }
    // 如果在网页全屏模式，先退出网页全屏
    else if (artPlayer.value.fullscreenWeb) {
      artPlayer.value.fullscreenWeb = false;
      setTimeout(() => {
        showEpisodesList.value = true;
      }, 300);
    }
    else {
      toggleEpisodesList();
    }
  } else {
    toggleEpisodesList();
  }
};

// 播放指定剧集
const playEpisode = async (episodeName, url) => {
  currentEpisode.value = episodeName;
  currentUrl.value = url;

  // 设置当前集数索引 - 同时匹配名称和URL以确保唯一性
  const index = currentSourceEpisodes.value.findIndex(ep => ep.name === episodeName && ep.url === url);
  if (index !== -1) {
    currentEpisodeIndex.value = index;
  }

  // 初始化播放器
  initPlayer();
  // 自动进入全屏
  if (autoFullscreen.value) {
    const is_full_scren = await appWindow.isFullscreen();
    console.log(is_full_scren, 'is_fullscreen')
    if (!is_full_scren){
        await appWindow.setFullscreen(true)
    }
    artPlayer.value.fullscreenWeb = true;
  }

  // 保存播放记录
  savePlayRecord();

  // 更新分页以确保当前集数在可见范围内
  updatePageForCurrentEpisode();
};

// 播放上一集
const playPreviousEpisode = () => {
  if (hasPreviousEpisode.value) {
    const prevIndex = currentEpisodeIndex.value - 1;
    const prevEpisode = currentSourceEpisodes.value[prevIndex];
    playEpisode(prevEpisode.name, prevEpisode.url);
    currentEpisodeIndex.value = prevIndex;
  }
};

// 播放下一集
const playNextEpisode = () => {
  if (hasNextEpisode.value) {
    const nextIndex = currentEpisodeIndex.value + 1;
    const nextEpisode = currentSourceEpisodes.value[nextIndex];
    playEpisode(nextEpisode.name, nextEpisode.url);
    currentEpisodeIndex.value = nextIndex;
  }
};

// 保存播放记录
const savePlayRecord = async () => {
  // 确保有有效的播放时间
  if (currentTime.value <= 0) {
    console.log('跳过保存记录：播放时间为0');
    return;
  }
  
  const record = {
    title: title.value,
    vodYear: movie.value.vod_year || '',
    source: currentSource.value,
    episode: currentEpisode.value,
    url: currentUrl.value,
    currentTime: currentTime.value,
    movie: movie.value // 添加完整的movie对象
  };
  console.log('保存播放记录:', {
    title: record.title,
    episode: record.episode,
    currentTime: record.currentTime
  });
  
  // 使用 store 保存播放记录
  await playHistoryStore.savePlayRecord(record);
};

// 初始化播放器
const initPlayer = () => {
  if (artPlayer.value) {
    artPlayer.value.destroy();
  }

  // 如果没有URL，则不创建播放器
  if (!currentUrl.value) return;
  
  // 播放器配置
  const playerOptions = {
    container: artPlayerRef.value,
    url: currentUrl.value,
    title: `${title.value} - ${currentEpisode.value}`,
    volume: 0.8,
    isLive: false,
    muted: false,
    autoplay: true,
    pip: true,
    autoSize: true,
    autoMini: true,
    screenshot: false,
    setting: true,
    playbackRate: true,
    aspectRatio: true,
    sfullscreen: true,
    fullscreenWeb: true,
    miniProgressBar: true,
    mutex: true,
    backdrop: true,
    playsInline: true,
    autoPlayback: true,
    airplay: false,
    theme: '#23ade5',
    lang: 'zh-cn',
    whitelist: ['*'],
    hotkey: false,
    lock: true,
    fastForward: true,
    autoOrientation: true,
    moreVideoAttr: {
      crossOrigin: 'anonymous',
      controlsList: 'nodownload',
    },
    settings: [
          {
            html: '自动播放下一集',
            tooltip: '',
            icon: '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L2 22h20L12 2z"></path><path d="M12 2L2 22h20L12 2z"></path></svg>',
            switch: autoPlayNext.value,
            onSwitch: function (item, $dom, event) {
                const nextState = !item.switch;
                autoPlayNext.value = nextState;
                localStorage.setItem('autoPlayNext', nextState);
                return nextState;
            },
        },
        {
            html: '自动全屏',
            tooltip: '',
            icon: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="#ffffff" d="M9.367 2.25H9.4a.75.75 0 0 1 0 1.5c-1.132 0-1.937 0-2.566.052c-.62.05-1.005.147-1.31.302a3.25 3.25 0 0 0-1.42 1.42c-.155.305-.251.69-.302 1.31c-.051.63-.052 1.434-.052 2.566a.75.75 0 0 1-1.5 0v-.033c0-1.092 0-1.958.057-2.655c.058-.714.18-1.317.46-1.868a4.75 4.75 0 0 1 2.077-2.076c.55-.281 1.154-.403 1.868-.461c.697-.057 1.563-.057 2.655-.057m7.8 1.552c-.63-.051-1.435-.052-2.567-.052a.75.75 0 0 1 0-1.5h.033c1.092 0 1.958 0 2.655.057c.714.058 1.317.18 1.869.46a4.75 4.75 0 0 1 2.075 2.077c.281.55.403 1.154.461 1.868c.057.697.057 1.563.057 2.655V9.4a.75.75 0 0 1-1.5 0c0-1.132 0-1.937-.052-2.566c-.05-.62-.147-1.005-.302-1.31a3.25 3.25 0 0 0-1.42-1.42c-.305-.155-.69-.251-1.31-.302m-5.502 3.527a.75.75 0 0 1 .67 0l4 2a.75.75 0 0 1 .415.671v4a.75.75 0 0 1-.415.67l-4 2a.75.75 0 0 1-.67 0l-4-2A.75.75 0 0 1 7.25 14v-4a.75.75 0 0 1 .415-.67zM3 13.85a.75.75 0 0 1 .75.75c0 1.133 0 1.937.052 2.566c.05.62.147 1.005.302 1.31a3.25 3.25 0 0 0 1.42 1.42c.305.155.69.251 1.31.302c.63.051 1.434.052 2.566.052a.75.75 0 0 1 0 1.5h-.033c-1.092 0-1.958 0-2.655-.057c-.714-.058-1.317-.18-1.868-.46a4.75 4.75 0 0 1-2.076-2.076c-.281-.552-.403-1.155-.461-1.869c-.057-.697-.057-1.563-.057-2.655V14.6a.75.75 0 0 1 .75-.75m18 0a.75.75 0 0 1 .75.75v.033c0 1.092 0 1.958-.057 2.655c-.058.714-.18 1.317-.46 1.869a4.75 4.75 0 0 1-2.076 2.075c-.552.281-1.155.403-1.869.461c-.697.057-1.563.057-2.655.057H14.6a.75.75 0 0 1 0-1.5c1.133 0 1.937 0 2.566-.052c.62-.05 1.005-.147 1.31-.302a3.25 3.25 0 0 0 1.42-1.42c.155-.305.251-.69.302-1.31c.051-.63.052-1.434.052-2.566a.75.75 0 0 1 .75-.75"/></svg>',
            switch: autoFullscreen.value,
            onSwitch: function (item, $dom, event) {
                const nextState = !item.switch;
                autoFullscreen.value = nextState;
                localStorage.setItem('autoFullscreen', nextState);
                return nextState;
            },
        },
        {
            html: '跳过片头',
            tooltip: '',
            icon: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="#ffffff" d="M6.962 2.421c1.276-.171 2.908-.171 4.981-.171h.114c2.073 0 3.705 0 4.98.171c1.31.176 2.354.545 3.175 1.367c.822.821 1.19 1.866 1.367 3.174c.171 1.276.171 2.908.171 4.981v.114c0 2.073 0 3.705-.171 4.98c-.176 1.31-.545 2.354-1.367 3.175c-.821.822-1.866 1.19-3.174 1.367c-1.276.171-2.908.171-4.981.171h-.114c-2.073 0-3.705 0-4.98-.171c-1.31-.176-2.354-.545-3.175-1.367c-.822-.821-1.19-1.866-1.367-3.174c-.171-1.276-.171-2.908-.171-4.981v-.114c0-2.073 0-3.705.171-4.98c.176-1.31.545-2.354 1.367-3.175c.821-.822 1.866-1.19 3.174-1.367m4.773 4.432a.75.75 0 0 0-1.431-.132L8.492 11.25H7a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 .696-.472l1.063-2.657l1.506 7.526a.75.75 0 0 0 1.431.132l1.812-4.529H17a.75.75 0 0 0 0-1.5h-2a.75.75 0 0 0-.696.471L13.24 14.38z"/></svg>',
            switch: skipIntro.value,
            onSwitch: function (item, $dom, event) {
                const nextState = !item.switch;
                skipIntro.value = nextState;
                localStorage.setItem('skipIntro', nextState);
                
                // 如果开启了跳过片头，弹出设置对话框
                if (nextState) {
                  const currentTime = skipTime.value;
                  ElMessageBox.prompt('请输入跳过片头的时间（秒）', '跳过片头设置', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  inputPattern: /^\d+$/,
                  inputErrorMessage: '请输入数字',
                })
                  .then(({ value }) => {
                    if (value !== null) {
                      const timeValue = parseInt(value);
                      if (!isNaN(timeValue) && timeValue > 0) {
                        skipTime.value = timeValue;
                        localStorage.setItem('skipTime', timeValue);
                      }
                    }
                  })
                  .catch(() => {
                    skipTime.value = currentTime;
                    localStorage.setItem('skipTime', currentTime);
                  });
                }
                
                return nextState;
            },
        },
        {
            html: '跳过片尾',
            tooltip: '',
            icon: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="#ffffff" d="M6.962 2.421c1.276-.171 2.908-.171 4.981-.171h.114c2.073 0 3.705 0 4.98.171c1.31.176 2.354.545 3.175 1.367c.822.821 1.19 1.866 1.367 3.174c.171 1.276.171 2.908.171 4.981v.114c0 2.073 0 3.705-.171 4.98c-.176 1.31-.545 2.354-1.367 3.175c-.821.822-1.866 1.19-3.174 1.367c-1.276.171-2.908.171-4.981.171h-.114c-2.073 0-3.705 0-4.98-.171c-1.31-.176-2.354-.545-3.175-1.367c-.822-.821-1.19-1.866-1.367-3.174c-.171-1.276-.171-2.908-.171-4.981v-.114c0-2.073 0-3.705.171-4.98c.176-1.31.545-2.354 1.367-3.175c.821-.822 1.866-1.19 3.174-1.367m4.773 4.432a.75.75 0 0 0-1.431-.132L8.492 11.25H7a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 .696-.472l1.063-2.657l1.506 7.526a.75.75 0 0 0 1.431.132l1.812-4.529H17a.75.75 0 0 0 0-1.5h-2a.75.75 0 0 0-.696.471L13.24 14.38z"/></svg>',
            switch: skipOutro.value,
            onSwitch: function (item, $dom, event) {
                const nextState = !item.switch;
                skipOutro.value = nextState;
                localStorage.setItem('skipOutro', nextState);
                
                 // 如果开启了跳过片头，弹出设置对话框
                 if (nextState) {
                  const currentTime = skipOutroTime.value;
                  ElMessageBox.prompt('请输入跳过片尾的时间（秒）', '跳过片尾设置', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  inputPattern: /^\d+$/,
                  inputErrorMessage: '请输入数字',
                })
                  .then(({ value }) => {
                    if (value !== null) {
                      const timeValue = parseInt(value);
                      if (!isNaN(timeValue) && timeValue > 0) {
                        skipOutroTime.value = timeValue;
                        localStorage.setItem('skipOutroTime', timeValue);
                      }
                    }
                  })
                  .catch(() => {
                    skipOutroTime.value = currentTime;
                    localStorage.setItem('skipOutroTime', currentTime);
                  });
                }
                return nextState;
            },
        },
    ],
    controls: [
      {
        name: 'toggle-episodes',
        position: 'right',
        html: '选集',
        click: function () {
          safeToggleEpisodesList();
        },
        style: { opacity: 1, cursor: 'pointer' },
      },
      {
        name: 'previous-episode',
        position: 'left',
        index: 1,
        html: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 19 2 12 11 5 11 19"></polygon><polygon points="22 19 13 12 22 5 22 19"></polygon></svg>',
        tooltip: '上一集',
        click: function () {
          if (hasPreviousEpisode.value) {
            playPreviousEpisode();
          } else {
            artPlayer.value.notice.show = '已经是第一集了';
          }
        },
        style: { opacity: 1, cursor: 'pointer' },
      },
      {
        name: 'next-episode',
        position: 'left',
        index: 13,
        html: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="13 5 22 12 13 19 13 5"></polygon><polygon points="2 5 11 12 2 19 2 5"></polygon></svg>',
        tooltip: '下一集',
        click: function () {
          if (hasNextEpisode.value) {
            playNextEpisode();
          } else {
            artPlayer.value.notice.show = '已经是最后一集了';
          }
        },
        style: { opacity: 1, cursor: 'pointer' },
      },
    ],
    customType: {
      m3u8: function (video, url) {
        if (Hls.isSupported()) {
          const hls = new Hls({
            debug: false, // 关闭日志
            enableWorker: true,
            lowLatencyMode: false,
            backBufferLength: 90,
            maxBufferLength: 30,
            maxMaxBufferLength: 60,
            maxBufferSize: 30 * 1000 * 1000,
            maxBufferHole: 0.5,
            fragLoadingMaxRetry: 6,
            fragLoadingMaxRetryTimeout: 64000,
            fragLoadingRetryDelay: 1000,
            manifestLoadingMaxRetry: 3,
            manifestLoadingRetryDelay: 1000,
            levelLoadingMaxRetry: 4,
            levelLoadingRetryDelay: 1000,
            startLevel: -1,
            abrEwmaDefaultEstimate: 500000,
            abrBandWidthFactor: 0.95,
            abrBandWidthUpFactor: 0.7,
            abrMaxWithRealBitrate: true,
            stretchShortVideoTrack: true,
            appendErrorMaxRetry: 5,  // 增加尝试次数
            liveSyncDurationCount: 3,
            liveDurationInfinity: false
          }
          );
          hls.loadSource(url);
          hls.attachMedia(video);

          // 清除实例
          artPlayer.value.on('destroy', () => {
            hls.destroy();
          });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          video.src = url;
        } else {
          artPlayer.value.notice.show = '您的浏览器不支持播放HLS视频';
        }
      },
    },
  };
  // 初始化播放器
  artPlayer.value = new Artplayer(playerOptions);
  // 监听网页全屏切换事件
  artPlayer.value.on('webfullscreen',  async (isFullscreen) => {
    if (await appWindow.isFullscreen()){
      await appWindow.setFullscreen(false)
    }
    // 退出网页全屏时重置播放器尺寸
    if (!isFullscreen) {
      setTimeout(() => {
        if (artPlayer.value) {
          artPlayer.value.resize();
        }
      }, 200);
    }
  });

  let hideTimer;

  // 隐藏控制栏
  function hideControls() {
    if (artPlayer.value && artPlayer.value.controls) {
      artPlayer.value.controls.show = false;
    }
  }

  // 重置计时器，计时器超时时间
  function resetHideTimer() {
    clearTimeout(hideTimer);
    hideTimer = setTimeout(() => {
      hideControls();
    }, 2000);
  }

  // 处理鼠标离开浏览器窗口
  function handleMouseOut(e) {
    if (e && !e.relatedTarget) {
      resetHideTimer();
    }
  }

  // 全屏状态切换时注册/移除 mouseout 事件，监听鼠标移出屏幕事件
  // 从而对播放器状态栏进行隐藏倒计时
  function handleFullScreen(isFullScreen, isWeb) {
    if (isFullScreen) {
      document.addEventListener('mouseout', handleMouseOut);
    } else {
      document.removeEventListener('mouseout', handleMouseOut);
      // 退出全屏时清理计时器
      clearTimeout(hideTimer);
    }

    if (!isWeb) {
      if (window.screen.orientation && window.screen.orientation.lock) {
        window.screen.orientation.lock('landscape')
          .then(() => {
          })
          .catch((error) => {
          });
      }
    }
  }

  // 播放器加载完成后初始隐藏工具栏
  artPlayer.value.on('ready', () => {
    hideControls();
  });

  // 监听视频是否播放完成
  artPlayer.value.on('video:ended', () => {
    if (autoPlayNext.value) {
      playNextEpisode();
    }
  });

  // 添加鼠标移入移出事件监听
  artPlayer.value.on('mousemove', () => {
    if (artPlayer.value && artPlayer.value.controls) {
      artPlayer.value.controls.show = true;
      resetHideTimer();
    }
  });

  // 监听鼠标进入播放器区域
  artPlayer.value.on('mouseenter', () => {
    if (artPlayer.value && artPlayer.value.controls) {
      artPlayer.value.controls.show = true;
    }
  });

  // 监听鼠标离开播放器区域
  artPlayer.value.on('mouseleave', () => {
    hideControls();
  });

  // 全屏 Web 模式处理
  artPlayer.value.on('fullscreenWeb', function (isFullScreen) {
    handleFullScreen(isFullScreen, true);
  });

  // 全屏模式处理
  artPlayer.value.on('fullscreen', function (isFullScreen) {
    handleFullScreen(isFullScreen, false);
  });

  artPlayer.value.on('video:timeupdate', () => {
    currentTime.value = artPlayer.value.currentTime;
    
    // 处理跳过片头
    if (skipIntro.value && artPlayer.value.currentTime > 0 && artPlayer.value.currentTime < skipTime.value) {
      artPlayer.value.seek = skipTime.value;
      artPlayer.value.notice.show = `已跳过片头 ${skipTime.value} 秒`;
    }
    
    // 处理跳过片尾
    if (skipOutro.value && artPlayer.value.duration > 0) {
      const remainingTime = artPlayer.value.duration - artPlayer.value.currentTime;
      if (remainingTime <= skipOutroTime.value && remainingTime > 0) {
        if (autoPlayNext.value && hasNextEpisode.value) {
          playNextEpisode();
          artPlayer.value.notice.show = '已跳过片尾并播放下一集';
        } else if (!autoPlayNext.value) {
          artPlayer.value.seek = artPlayer.value.duration;
          artPlayer.value.notice.show = `已跳过片尾 ${skipOutroTime.value} 秒`;
        }
      }
    }
  });

  // 每30秒保存一次播放记录
  const saveInterval = setInterval(() => {
    if (currentTime.value > 0) {
      savePlayRecord();
    }
  }, 30000);

  // 监听暂停事件，保存播放进度
  artPlayer.value.on('pause', () => {
    if (currentTime.value > 0) {
      savePlayRecord();
    }
  });

  // 监听视频结束事件，保存播放进度
  artPlayer.value.on('ended', () => {
    if (currentTime.value > 0) {
      savePlayRecord();
    }
  });

  // 销毁时清除定时器
  artPlayer.value.on('destroy', () => {
    // 销毁前保存最后的播放进度
    if (currentTime.value > 0) {
      savePlayRecord();
    }
    clearInterval(saveInterval);
  });
};

// 监听源切换
watch(currentSource, (newSource) => {
  const source = sources.value.find(s => s.sourceName === newSource);
  if (source && source.episodes.length > 0) {
    // 保存当前播放时间
    const currentPlaybackTime = artPlayer.value ? artPlayer.value.currentTime : 0;

    // 尝试找到与当前播放集数名称相匹配的剧集
    if (currentEpisode.value) {
      const matchingEpisode = source.episodes.find(ep => ep.name === currentEpisode.value);
      if (matchingEpisode) {
        // 找到匹配的剧集，无感切换
        playEpisode(matchingEpisode.name, matchingEpisode.url);

        // 恢复播放进度
        setTimeout(() => {
          if (artPlayer.value && currentPlaybackTime > 0) {
            artPlayer.value.seek = currentPlaybackTime;
          }
        }, 1000);

        return;
      }
    }

    // 如果没有找到匹配的剧集，则查找近似集数
    const currentEpisodeNumber = extractEpisodeNumber(currentEpisode.value);
    if (currentEpisodeNumber) {
      // 尝试找到相近的集数
      for (const ep of source.episodes) {
        const epNumber = extractEpisodeNumber(ep.name);
        if (epNumber === currentEpisodeNumber) {
          // 找到相近的集数
          playEpisode(ep.name, ep.url);

          // 恢复播放进度
          setTimeout(() => {
            if (artPlayer.value && currentPlaybackTime > 0) {
              artPlayer.value.seek = currentPlaybackTime;
            }
          }, 1000);

          return;
        }
      }
    }

    // 如果找不到匹配的或相近的剧集，则从第一集开始播放
    playEpisode(source.episodes[0].name, source.episodes[0].url);
    // 重置分页到第一页
    currentPage.value = 1;
  }
});

// 监听当前剧集索引变化，确保它在可见页面中
watch(currentEpisodeIndex, () => {
  updatePageForCurrentEpisode();
});

// 更新分页以确保当前集数在可见范围内
const updatePageForCurrentEpisode = () => {
  // 计算当前集数应该在哪一页
  const targetPage = Math.floor(currentEpisodeIndex.value / pageSize) + 1;

  // 如果不在当前页，则切换页面
  if (currentPage.value !== targetPage) {
    currentPage.value = targetPage;
  }
};

// 初始化
onMounted(async () => {
  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleKeydown);

  // 如果路由中有URL参数，则直接播放
  if (currentUrl.value) {
    initPlayer();

    // 设置当前集数索引，确保同时匹配名称和URL
    const index = currentSourceEpisodes.value.findIndex(ep =>
      ep.name === currentEpisode.value && ep.url === currentUrl.value);
    if (index !== -1) {
      currentEpisodeIndex.value = index;
    }

    // 检查是否有播放记录，如果有，则跳转到上次播放位置
    const record = await playHistoryStore.getPlayRecordByVodNameYear(title.value, movie.value.vod_year);
    console.log(record, 'record')
    if (record.length > 0 && record[0].currentPlaybackTime > 0) {
      // 延迟一点执行，确保播放器已初始化
      setTimeout(() => {
        if (artPlayer.value) {
          artPlayer.value.seek = record[0].currentPlaybackTime || 0;
          // 同步更新当前时间变量
          currentTime.value = record[0].currentPlaybackTime || 0;
        }
      }, 1000);
    }

    // 确保正确的页面被显示
    updatePageForCurrentEpisode();
  }
  // 如果没有URL参数但有sources数据，则播放第一个源的第一集
  else if (sources.value.length > 0 && sources.value[0].episodes.length > 0) {
    const firstSource = sources.value[0];
    currentSource.value = firstSource.sourceName;
    playEpisode(firstSource.episodes[0].name, firstSource.episodes[0].url);
  }
});

// 销毁播放器
onBeforeUnmount(() => {
  // 移除全局键盘事件监听
  document.removeEventListener('keydown', handleKeydown);

  if (artPlayer.value) {
    // 保存最后的播放记录
    savePlayRecord();
    // 移除窗口大小变化监听
    window.removeEventListener('resize', () => {
      if (artPlayer.value) {
        artPlayer.value.resize();
      }
    });
    artPlayer.value.destroy();
  }
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 全局键盘事件处理函数
const handleKeydown = (event) => {
  // 如果播放器未初始化，则不处理
  if (!artPlayer.value) return;

  // 根据按键执行相应操作
  switch (event.key.toLowerCase()) {
    case ' ':  // 空格：播放/暂停
      artPlayer.value.playing ? artPlayer.value.pause() : artPlayer.value.play();
      event.preventDefault();
      break;
    case 'arrowleft':  // 左箭头：后退5秒
      artPlayer.value.seek = Math.max(0, artPlayer.value.currentTime - 5);
      event.preventDefault();
      break;
    case 'arrowright':  // 右箭头：前进5秒
      artPlayer.value.seek = Math.min(artPlayer.value.duration, artPlayer.value.currentTime + 5);
      event.preventDefault();
      break;
    case 'arrowup':  // 上箭头：增加音量
      const newVolumeUp = Math.min(1, artPlayer.value.volume + 0.1);
      artPlayer.value.volume = newVolumeUp;
      artPlayer.value.notice.show = `音量: ${Math.round(newVolumeUp * 100)}%`;
      event.preventDefault();
      break;
    case 'arrowdown':  // 下箭头：减少音量
      const newVolumeDown = Math.max(0, artPlayer.value.volume - 0.1);
      artPlayer.value.volume = newVolumeDown;
      artPlayer.value.notice.show = `音量: ${Math.round(newVolumeDown * 100)}%`;
      event.preventDefault();
      break;
    case 'escape':  // ESC键处理
      // 如果在网页全屏，先退出全屏
      if (artPlayer.value.fullscreenWeb) {
        artPlayer.value.fullscreenWeb = false;
      }
      event.preventDefault();
      break;
    case 'pageup':  // PageUp：上一集
      if (hasPreviousEpisode.value) {
        playPreviousEpisode();
      } else {
        artPlayer.value.notice.show = '已经是第一集了';
      }
      event.preventDefault();
      break;
    case 'pagedown':  // PageDown：下一集
      if (hasNextEpisode.value) {
        playNextEpisode();
      } else {
        artPlayer.value.notice.show = '已经是最后一集了';
      }
      event.preventDefault();
      break;
  }
};

</script>

<style scoped>
.player-page {
  padding: 0;
  min-height: calc(100vh - 48px);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-bar {
  display: flex;
  align-items: center;
  padding: 12px 10px 15px;
  margin-bottom: 10px;
  background-color: #f1f5fa;
  border-radius: 0;
  width: 100%;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.back-button .el-icon {
  font-size: 22px;
  color: var(--el-color-primary);
}

.title-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding-right: 40px;
  /* Balance for back button */
}

.video-title {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
}

.player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  flex: 1;
  padding: 0 20px 10px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.video-info {
  margin-bottom: 5px;
}

.video-title {
  margin: 0 0 5px 0;
  font-size: 20px;
}

.video-meta {
  margin: 0;
  color: #999;
  font-size: 13px;
}

.player-wrapper-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.player-wrapper {
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 120px);
  background-color: #000;
  margin: auto;
  overflow: hidden;
  position: relative;
  /* 确保定位上下文 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.art-player {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 上下居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.drawer-content {
  padding: 0 10px;
  height: 100%;
  overflow-y: auto;
}

/* Drawer header styles */
.drawer-header {
  display: flex;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.tab-title {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
}

.tab-title.active {
  color: var(--el-color-primary);
}

.tab-title.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--el-color-primary);
}

.source-container {
  margin-bottom: 20px;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #e0e0e0;
}

.source-item.active {
  background-color: #f0f9ff;
  border-color: var(--el-color-primary);
}

.source-name {
  font-weight: 500;
}

.episode-count {
  font-size: 12px;
  color: #999;
}

.episodes-grid {
  margin-top: 15px;
}

.page-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-number {
  font-size: 14px;
  color: #666;
  margin: 0 5px;
}

.episodes-wrapper {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.episode-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 4px;
  background-color: #f5f5f5;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
}

.episode-item.active {
  background-color: var(--el-color-primary);
  color: #fff;
}

@media (max-width: 768px) {
  .player-page {
    padding: 10px;
  }

  .player-wrapper {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
  }

  .episodes-wrapper {
    grid-template-columns: repeat(4, 1fr);
  }

  /* Make drawer wider on mobile */
  :deep(.el-drawer) {
    width: 80% !important;
  }
}

/* 确保抽屉在全屏模式下也能正常显示 */
:deep(.el-drawer) {
  z-index: 10000 !important;
}
</style>
