<template>
    <div class="settings-page">
      <div class="settings-container">
        <div class="settings-sidebar">
          <el-menu
            :default-active="activeSettingsTab"
            class="settings-menu"
            @select="handleSettingsTabChange"
          >
            <el-menu-item index="basic">
              <el-icon><Tools /></el-icon>
              <span>基础配置</span>
            </el-menu-item>
            <el-menu-item index="resources">
              <el-icon><Management /></el-icon>
              <span>资源管理配置</span>
            </el-menu-item>
          </el-menu>
        </div>
        
        <div class="settings-content">
          <!-- 基础配置 -->
          <div v-show="activeSettingsTab === 'basic'">
            <h3>基础配置</h3>
            <el-form label-width="120px">
              <el-form-item label="主题选择">
                <el-radio-group :model-value="themeValue" @update:model-value="setTheme">
                  <el-radio value="light">浅色</el-radio>
                  <el-radio value="dark">深色</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="语言设置">
                <el-select v-model="languageValue" @change="setLanguage">
                  <el-option label="简体中文" value="zh-CN"></el-option>
                  <!-- <el-option label="English" value="en-US"></el-option> -->
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 资源管理配置 -->
          <div v-show="activeSettingsTab === 'resources'">
            <h3>资源管理配置</h3>
            <div v-if="resourcesLoading">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else>
              <div style="display: flex; justify-content: flex-end; margin-bottom: 16px;">
                <el-button type="primary" size="small" @click="openResourceDialog()">新增资源</el-button>
              </div>
              <el-table :data="paginatedResources" style="width: 100%" v-if="resources && resources.length > 0">
                <el-table-column prop="name" label="名称"></el-table-column>
                <el-table-column prop="api" label="API地址"></el-table-column>
                <el-table-column prop="status" label="状态">
                  <template #default="scope">
                    <el-switch v-model="scope.row.status"></el-switch>
                  </template>
                </el-table-column>
                <el-table-column label="默认">
                  <template #default="scope">
                    <el-switch 
                      :model-value="scope.row.isDefault" 
                      @change="handleSetDefaultResource(scope.row.id, scope.row.isDefault)"
                      label-position="left"
                    ></el-switch>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                    <el-button type="primary" link size="small" @click="openResourceDialog(scope.row.id)">编辑</el-button>
                    <el-button type="danger" link size="small" @click="handleRemoveResource(scope.row.id)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                v-if="resources && resources.length > 0"
                class="pagination"
                background
                layout="prev, pager, next"
                :total="totalResources"
                :page-size="pageSize"
                :current-page="currentPage"
                @current-change="handleCurrentChange"
              />
              <el-empty v-else description="暂无资源">
                <el-button type="primary" @click="openResourceDialog()">添加资源</el-button>
              </el-empty>
              
              <!-- 资源表单对话框 -->
              <el-dialog
                v-model="resourceDialogVisible"
                :title="isEditing ? '编辑资源' : '添加资源'"
                width="50%"
                :destroy-on-close="false"
                append-to-body
              >
                <el-form label-width="80px">
                  <el-form-item label="名称">
                    <el-input v-model="newResource.name" placeholder="请输入资源名称"></el-input>
                  </el-form-item>
                  <el-form-item label="API地址">
                    <el-input v-model="newResource.api" placeholder="请输入API地址"></el-input>
                  </el-form-item>
                  <el-form-item label="状态">
                    <el-switch v-model="newResource.status"></el-switch>
                  </el-form-item>
                  <el-form-item label="默认" v-if="!isEditing">
                    <el-switch v-model="newResource.isDefault" :label="true">设为默认</el-switch>
                  </el-form-item>
                </el-form>
                <template #footer>
                  <span class="dialog-footer">
                    <el-button @click="resourceDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveResource">确认</el-button>
                  </span>
                </template>
              </el-dialog>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
import { useSettingsStore } from '../stores/settingsStore';
import { useNavStore } from '../stores/navStore';
import message from '../utils/message';
import { ElMessageBox } from 'element-plus';
  
  import { 
    updateResource, 
    deleteResource, 
    addResource, 
    getResources, 
    updateResourceDefault,
    countResources
  } from '../db/resources';
  import { Tools, Management } from '@element-plus/icons-vue';
  
  // 设置页面控制
const resourceDialogVisible = ref(false);
const activeSettingsTab = ref('basic');

// 设置相关
const settingsStore = useSettingsStore();
const navStore = useNavStore();
const settings = computed(() => settingsStore.settings);
const settingsLoading = computed(() => settingsStore.loading);
const themeValue = computed(() => settingsStore.settings?.theme || 'dark');
const languageValue = computed(() => settingsStore.settings?.language || 'zh-CN');
  
  // 资源相关
  const resources = ref([]);
  const resourcesLoading = ref(false);
  const editingId = ref(null);
  const newResource = reactive({
    name: '',
    api: '',
    status: true,
    isDefault: false
  });
  
  // 分页相关
  const currentPage = ref(1);
  const pageSize = ref(8);
  const totalResources = ref(0);
  
  // 计算属性
  const isEditing = computed(() => editingId.value !== null);
  const paginatedResources = computed(() => resources.value);
  
  // 方法
  function handleSettingsTabChange(tab) {
  activeSettingsTab.value = tab;
}
  
  // 设置相关方法
  const setTheme = (value) => {
    settingsStore.setTheme(value);
  };
  
  const setLanguage = (value) => {
    settingsStore.setLanguage(value);
  };
  
  // 资源相关方法
  async function fetchResources() {
    try {
      resourcesLoading.value = true;
      const offset = (currentPage.value - 1) * pageSize.value;
      resources.value = await getResources(offset, pageSize.value);
      
      // 获取资源总数
      totalResources.value = await countResources();
    } catch (error) {
      console.error('获取资源失败:', error);
    } finally {
      resourcesLoading.value = false;
    }
  }
  
  function handleCurrentChange(page) {
    currentPage.value = page;
    fetchResources();
  }
  
  function resetResourceForm() {
    newResource.name = '';
    newResource.api = '';
    newResource.status = true;
    newResource.isDefault = false;
    editingId.value = null;
  }
  
  function openResourceDialog(id = null) {
    resetResourceForm();
    if (id) {
      editResource(id);
    }
    resourceDialogVisible.value = true;
  }
  
  function editResource(id) {
    const resource = resources.value.find(r => r.id === id);
    if (resource) {
      newResource.name = resource.name;
      newResource.api = resource.api;
      newResource.status = resource.status;
      editingId.value = id;
      resourceDialogVisible.value = true;
    }
  }
  
  async function saveResource() {
    if (!newResource.name || !newResource.api) {
      message.warning('请填写完整资源信息');
      return;
    }
    
    const resourceData = {
      name: newResource.name,
      api: newResource.api,
      status: newResource.status,
      // 在添加/编辑时先不设置默认状态，后续单独处理
      isDefault: false
    };
    
    try {
      let savedResourceId = null;
      
      if (isEditing.value) {
        // 先更新资源基本信息
        await updateResource(editingId.value, resourceData);
        savedResourceId = editingId.value;
        
        // 如果正在编辑的是当前使用的资源，更新navStore中的资源信息
        if (savedResourceId === navStore.currentResourceId) {
          navStore.clearResourceCache(savedResourceId);
          navStore.setCurrentResource({...resourceData, id: savedResourceId});
        }
        
        // 如果编辑时设置了默认，单独处理默认状态
        if (newResource.isDefault) {
          await updateResourceDefault(editingId.value, true);
        }
      } else {
        // 先添加资源
        await addResource(resourceData);
        
        // 如果是默认资源，单独更新默认状态
        if (newResource.isDefault) {
          // 获取刚添加的资源ID
          const allResources = await getResources(0, 1);
          if (allResources && allResources.length > 0) {
            savedResourceId = allResources[0].id;
            await updateResourceDefault(savedResourceId, true);
          }
        }
        
        // 如果添加成功，跳转到第一页
        currentPage.value = 1;
      }
      
      // 关闭对话框
      resourceDialogVisible.value = false;
      
      // 刷新资源列表
      await fetchResources();
      resetResourceForm();
      message.success(isEditing.value ? '更新成功' : '添加成功');
      
      // 如果设置了默认，显示额外提示和更新navStore
      if (newResource.isDefault && savedResourceId) {
        const updatedResource = await getResources(0, 1000).then(
          resources => resources.find(r => r.id === savedResourceId)
        );
        
        if (updatedResource) {
          navStore.clearAllCache(); // 清除所有缓存
          navStore.setCurrentResource(updatedResource);
        }
        
        message.info('已将该资源设为默认');
      }
    } catch (error) {
      console.error('保存资源失败:', error);
      message.error('操作失败，请重试');
    }
  }
  
  async function handleRemoveResource(id) {
    try {
      await ElMessageBox.confirm('确定要删除该资源吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      
      // 检查是否是当前正在使用的资源
      const isCurrentResource = navStore.currentResourceId === id;
      
      await deleteResource(id);
      
      // 清除该资源的分类缓存
      navStore.clearResourceCache(id);
      
      // 如果当前页没有数据了，且不是第一页，则跳转到上一页
      const totalCount = await countResources();
      const maxPage = Math.ceil(totalCount / pageSize.value);
      if (currentPage.value > maxPage && currentPage.value > 1) {
        currentPage.value = maxPage || 1;
      }
      
      await fetchResources();
      
      // 如果删除的是当前资源，需要重新设置默认资源
      if (isCurrentResource) {
        const newDefaultResource = resources.value.find(r => r.isDefault);
        if (newDefaultResource) {
          navStore.setCurrentResource(newDefaultResource);
        }
      }
      
      message.success('删除成功');
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除资源失败:', error);
        message.error('删除失败，请重试');
      }
    }
  }
  
  async function handleSetDefaultResource(id) {
    try {
      // 设置为默认资源，updateResourceDefault 函数内部会处理将其他资源设为非默认
      await updateResourceDefault(id, true);
      await fetchResources();
      
      // 更新当前默认资源到NavStore
      const newDefaultResource = resources.value.find(r => r.id === id);
      if (newDefaultResource) {
        navStore.setCurrentResource(newDefaultResource);
        navStore.clearAllCache(); // 清除所有缓存的分类数据
      }
      
      message.success(`已设置${newDefaultResource.name}为默认资源`);
    } catch (error) {
      console.error('设置默认资源失败:', error);
      message.error('设置失败，请重试');
    }
  }
  
  // 初始化
onMounted(async () => {
  settingsStore.fetchSettings();
  await fetchResources();
  
  // 获取默认资源，并设置到navStore
  const defaultResource = resources.value.find(r => r.isDefault);
  if (defaultResource) {
    navStore.setCurrentResource(defaultResource);
  }
});
  </script>
  
  <style scoped>
  .settings-page {
  padding: 20px;
  height: 100%;
}

.settings-container {
  display: flex;
  min-height: 500px;
}
  
  .settings-sidebar {
    width: 200px;
    border-right: 1px solid var(--border-color);
  }
  
  .settings-content {
    flex: 1;
    padding: 0 20px;
  }
  
  .settings-menu {
    height: 100%;
    border-right: none;
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .resource-form-card {
    margin-top: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  /* 优化设置菜单悬浮颜色 */
  .settings-menu :deep(.el-menu-item:hover) {
    background-color: var(--background-hover) !important;
    color: var(--primary-color) !important;
  }
  
  .settings-menu :deep(.el-menu-item.is-active) {
    background-color: rgba(76, 175, 80, 0.08) !important;
    color: var(--primary-color) !important;
  }
  
  :deep(html.dark) .settings-menu .el-menu-item:hover {
    background-color: var(--background-hover) !important;
    color: var(--primary-color) !important;
  }
  
  :deep(html.dark) .settings-menu .el-menu-item.is-active {
    background-color: rgba(76, 175, 80, 0.15) !important;
    color: var(--primary-color) !important;
  }
  </style> 