<template>
  <div v-if="loading" class="loading-container">
    <el-skeleton :rows="20" animated />
  </div>

  <div v-else-if="movie" class="movie-detail">
    <div class="movie-backdrop" :style="backdropStyle">
      <div class="backdrop-overlay"></div>
    </div>

    <div class="content-container">
      <div class="back-button" @click="router.back()">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      
      <div class="movie-header">
        <div class="movie-poster-container">
          <img :src="movie.vod_pic" :alt="movie.vod_name" class="movie-poster" />
        </div>
        
        <div class="movie-info-container">
          <h1 class="movie-title">{{ movie.vod_name }}</h1>
          
          <div class="movie-meta">
            <span class="movie-year">{{ movie.vod_year }}</span>
            <span class="meta-divider">|</span>
            <span class="movie-region">{{ movie.vod_area }}</span>
            <span class="meta-divider">|</span>
            <span class="movie-duration">{{ movie.vod_remarks }}</span>
            <span v-if="movie.vod_lang" class="meta-divider">|</span>
            <span v-if="movie.vod_lang" class="movie-lang">{{ movie.vod_lang }}</span>
          </div>
          
          <div class="movie-rating" v-if="movie.vod_douban_score && movie.vod_douban_score !== '0.0'">
            <span class="rating-score">{{ movie.vod_douban_score }}</span>
            <el-rate v-model="doubanScore" disabled show-score score-template="{value}" />
          </div>

          <!-- 影片详情信息 -->
          <div class="movie-details">
            <el-row :gutter="20" class="details-row">
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.type_name">
                <div class="detail-item">
                  <span class="detail-label">类型：</span>
                  <span class="detail-value">{{ movie.type_name }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_director">
                <div class="detail-item">
                  <span class="detail-label">导演：</span>
                  <span class="detail-value">{{ movie.vod_director }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_writer">
                <div class="detail-item">
                  <span class="detail-label">编剧：</span>
                  <span class="detail-value">{{ movie.vod_writer }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_actor">
                <div class="detail-item">
                  <span class="detail-label">主演：</span>
                  <span class="detail-value">{{ movie.vod_actor }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_pubdate">
                <div class="detail-item">
                  <span class="detail-label">上映日期：</span>
                  <span class="detail-value">{{ movie.vod_pubdate }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_total && movie.vod_total > 0">
                <div class="detail-item">
                  <span class="detail-label">总集数：</span>
                  <span class="detail-value">{{ movie.vod_total }}集</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_isend !== undefined">
                <div class="detail-item">
                  <span class="detail-label">完结状态：</span>
                  <span class="detail-value">{{ movie.vod_isend === 1 ? '已完结' : '连载中' }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_time">
                <div class="detail-item">
                  <span class="detail-label">更新时间：</span>
                  <span class="detail-value">{{ formatDateTime(movie.vod_time) }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_weekday">
                <div class="detail-item">
                  <span class="detail-label">更新周期：</span>
                  <span class="detail-value">{{ movie.vod_weekday }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_state">
                <div class="detail-item">
                  <span class="detail-label">资源状态：</span>
                  <span class="detail-value">{{ movie.vod_state }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_version">
                <div class="detail-item">
                  <span class="detail-label">影片版本：</span>
                  <span class="detail-value">{{ movie.vod_version }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="movie.vod_douban_id">
                <div class="detail-item">
                  <span class="detail-label">豆瓣ID：</span>
                  <span class="detail-value">
                    <a :href="`https://movie.douban.com/subject/${movie.vod_douban_id}/`" target="_blank">{{ movie.vod_douban_id }}</a>
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <!-- 剧情简介折叠面板 -->
          <div class="movie-summary">
            <el-collapse>
              <el-collapse-item title="剧情简介" name="summary">
                <div v-html="movie.vod_content"></div>
              </el-collapse-item>
            </el-collapse>
          </div>
          
          <div class="movie-actions">
            <el-button type="success" size="large" @click="playCurrentSource">
              <el-icon><VideoPlay /></el-icon>
              立即播放
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="movie-tabs">
        <el-tabs v-model="activeTab">
          <el-tab-pane v-for="(source, sourceIndex) in playSourcesList" 
            :key="sourceIndex" 
            :label="source.sourceName"
            :name="sourceIndex.toString()">
            <div class="episodes-header">
              <h3>选集列表</h3>
              <div class="episodes-actions">
                <el-button size="small" @click="toggleSortOrder(sourceIndex)" :icon="sortOrders[sourceIndex] ? 'SortDown' : 'SortUp'">
                  {{ sortOrders[sourceIndex] ? '倒序' : '正序' }}
                </el-button>
                <div class="episodes-count">共 {{ source.episodes.length }} 集</div>
              </div>
            </div>
            <div class="episodes-list">
              <el-button 
                v-for="(episode, episodeIndex) in source.episodes" 
                :key="episodeIndex"
                size="large"
                :class="{ 'episode-active': currentEpisode === episode.name }"
                :type="currentEpisode === episode.name ? 'success' : ''"
                @click="playEpisode(source.sourceName, episode.url, episode.name)"
              >
                <span class="episode-name">{{ episode.name }}</span>
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>

  <div v-else class="error-container">
    <el-result
      icon="error"
      title="影片不存在"
      sub-title="未找到对应的影片信息"
    >
      <template #extra>
        <el-button type="success" @click="router.push('/')">返回首页</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowLeft, VideoPlay, SortDown, SortUp } from '@element-plus/icons-vue';
import { useMovieStore } from '../stores/movieStore';

const movieStore = useMovieStore();

const route = useRoute();
const router = useRouter();
const movieId = computed(() => route.params.id);
const loading = ref(true);
const movie = ref(null);
const activeTab = ref('');
const currentEpisode = ref('');
const currentPlayUrl = ref('');
const playSourcesList = ref([]);
const sortOrders = ref({});

const doubanScore = computed(() => {
  if (movie.value && movie.value.vod_douban_score) {
    return parseFloat(movie.value.vod_douban_score) / 2; // 调整为5分制
  }
  return 0;
});

const backdropStyle = computed(() => {
  // 这里可以从电影详情获取背景图片，如果没有则使用海报
  return movie.value ? {
    backgroundImage: `url(${movie.value.vod_pic_slide || movie.value.vod_pic})`,
  } : {};
});


const playEpisode = (sourceName, url, episodeName) => {
  currentEpisode.value = episodeName;
  currentPlayUrl.value = url;  
  router.push({
    path: `/play/${movieId.value}`,
    query: { 
      title: movie.value.vod_name,
      source: sourceName,
      episode: episodeName,
      url: url,
      movie:JSON.stringify(movie.value)
    }
  });
};

const playCurrentSource = () => {
  // 默认播放第一个源的第一集
  if (playSourcesList.value.length > 0 && playSourcesList.value[0].episodes.length > 0) {
    const firstSource = playSourcesList.value[0];
    const firstEpisode = firstSource.episodes[0];
    playEpisode(firstSource.sourceName, firstEpisode.url, firstEpisode.name);
  }
};

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '';
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (err) {
    return dateStr;
  }
};

const toggleSortOrder = (sourceIndex) => {
  // 如果不存在，初始化为false（正序）
  if (sortOrders.value[sourceIndex] === undefined) {
    sortOrders.value[sourceIndex] = false;
  }
  
  // 切换排序顺序
  sortOrders.value[sourceIndex] = !sortOrders.value[sourceIndex];
  
  // 反转当前选中播放源的集数列表
  if (playSourcesList.value[sourceIndex]) {
    playSourcesList.value[sourceIndex].episodes = [...playSourcesList.value[sourceIndex].episodes].reverse();
  }
};

onMounted(async () => {
  loading.value = true;
  try {
    // 这里需要从配置或状态管理获取当前使用的API基础URL
    movie.value =  movieStore.movie;
    playSourcesList.value = movieStore.sources;
    console.log(playSourcesList.value, 'playSourcesList')
    console.log(movie.value, 'movie')
    // 如果有数据，默认选择第一个播放源的第一集
    if (playSourcesList.value.length > 0 && playSourcesList.value[0].episodes.length > 0) {
      activeTab.value = '0'; // 使用索引0作为第一个tab的name
    }
    
    // 初始化所有播放源的排序状态为正序（false）
    playSourcesList.value.forEach((_, index) => {
      sortOrders.value[index] = false;
    });
  } catch (error) {
    console.error('获取电影详情失败:', error);
    movie.value = null;
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.loading-container {
  padding: 30px;
}

.movie-detail {
  position: relative;
}


.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.back-button .el-icon {
  font-size: 22px;
  color:var(--el-color-primary);;
}

.movie-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400px;
  background-size: cover;
  background-position: center;
  z-index: 0;
}

.backdrop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.5), var(--background-color));
  z-index: 1;
}

.content-container {
  position: relative;
  z-index: 2;
  padding: 30px;
}

.movie-header {
  display: flex;
  margin-bottom: 40px;
}

.movie-poster-container {
  margin-right: 30px;
  flex-shrink: 0;
}

.movie-poster {
  width: 220px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.movie-info-container {
  flex: 1;
  padding-top: 20px;
}

.movie-title {
  font-size: 32px;
  margin: 0 0 10px 0;
  color: var(--text-color);
}

.movie-meta {
  font-size: 16px;
  color: var(--text-color-tertiary);
}

.meta-divider {
  margin: 0 10px;
}

.movie-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.rating-score {
  font-size: 24px;
  font-weight: bold;
  color: #ff9900;
  margin-right: 10px;
}

.movie-credits {
  margin-top: 20px;
  margin-bottom: 15px;
}

.movie-details {
  margin-bottom: 15px;
  padding-top: 15px;
}

.details-row .el-col {
  margin-bottom: 8px;
}

.movie-summary {
  margin-top: 15px;
  margin-bottom: 20px;
}

.movie-summary :deep(.el-collapse-item__header) {
  font-size: 16px;
  color: var(--text-color);
  font-weight: bold;
}

.movie-summary :deep(.el-collapse-item__content) {
  line-height: 1.8;
  color: var(--text-color);
  padding: 15px;
  background-color: var(--background-paper);
  border-radius: 4px;
}

.movie-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.movie-tabs {
  margin-top: 30px;
  margin-bottom: 40px;
  background-color: var(--background-paper);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.movie-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 10px;
}

.movie-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.movie-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
}

.movie-tabs :deep(.el-tabs__active-bar) {
  height: 3px;
}

.movie-tags {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.credits-list, .details-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.credit-item, .detail-item {
  display: flex;
  margin-bottom: 16px;
}

.credit-label, .detail-label {
  min-width: 80px;
  font-weight: bold;
  color: var(--text-color-secondary);
}

.credit-value, .detail-value {
  color: var(--text-color);
  flex: 1;
}

.episodes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px 0 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.episodes-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.episodes-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.episodes-count {
  color: var(--text-color-tertiary);
  font-size: 14px;
}

.episodes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 0 10px 0 10px;
  justify-content: flex-start;
}

.episodes-list .el-button {
  min-width: 110px;
  width: auto;
  height: 44px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  padding: 8px 16px;
  margin: 0;
  flex-grow: 0;
  flex-shrink: 0;
}

.episode-name {
  overflow: visible;
  white-space: nowrap;
  width: 100%;
  text-align: center;
}

.episodes-list .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.episodes-list .episode-active {
  transform: scale(1.03);
  box-shadow: 0 0 8px rgba(0, 200, 0, 0.2);
}

.error-container {
  padding: 50px 0;
}

@media (max-width: 400px) {
  .episodes-list {
    gap: 8px;
    justify-content: space-between;
  }
  
  .episodes-list .el-button {
    min-width: 0;
    width: calc(50% - 4px);
    height: 38px;
    font-size: 13px;
  }
  
  .episode-name {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

@media (min-width: 401px) and (max-width: 768px) {
  .episodes-list {
    gap: 10px;
  }
  
  .episodes-list .el-button {
    min-width: 100px;
    padding: 8px 10px;
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .episodes-list {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .movie-header {
    flex-direction: column;
  }
  
  .movie-poster-container {
    margin-right: 0;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
  
  .movie-tabs :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 15px;
  }
}

@media (min-width: 1200px) {
  .episodes-list {
    gap: 15px;
  }
}
</style> 