<template>
  <div class="app-container">
    <!-- Left Sidebar -->
    <div class="sidebar" :class="{ 'collapsed': isCollapse }">
      <div class="logo-container">
        <router-link to="/" class="logo">
          <img src="/logo.png" alt="灵象TV" class="logo-img" />
          <span class="logo-text" v-show="!isCollapse">灵象TV</span>
        </router-link>
      </div>
      
      <div class="menu">
        <el-menu
          :default-active="activeMenu"
          class="menu-items"
          :router="true"
          :collapse="isCollapse"
          text-color="var(--text-color)"
          active-text-color="var(--primary-color)"
          background-color="var(--background-paper)"
          :popper-append-to-body="false"
        >
          <el-menu-item index="/">
            <el-icon><HomeFilled /></el-icon>
            <template #title>首页</template>
          </el-menu-item>
          <el-menu-item index="/history">
            <el-icon><Clock /></el-icon>
            <template #title>播放历史</template>
          </el-menu-item>
          <el-menu-item index="/settings">
            <el-icon><Setting /></el-icon>
            <template #title>配置管理</template>
          </el-menu-item>
          <el-menu-item @click="openExternalLink('https://www.lingxiangtools.top/')">
            <el-icon><Present /></el-icon>
            <template #title>灵象工具箱</template>
          </el-menu-item>
          
        </el-menu>
      </div>
      
      <!-- 将折叠按钮移到侧边栏底部 -->
      <div class="sidebar-footer" @click="toggleCollapse">
        <el-button link type="success" >
          <el-icon v-if="isCollapse"><Expand /></el-icon>
          <el-icon v-else><Fold /></el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">  
      <div class="content">
        <router-view v-slot="{ Component, route }">
          <keep-alive>
            <component v-if="route.meta.keepAlive" :is="Component" />
          </keep-alive>
          <component v-if="!route.meta.keepAlive" :is="Component" />
        </router-view>
      </div>
    </div>
    
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useSettingsStore } from '../../stores/settingsStore';
import { Moon, Sunny } from '@element-plus/icons-vue';
import { openUrl } from '@tauri-apps/plugin-opener';

const route = useRoute();
const router = useRouter();
const settingsStore = useSettingsStore();

// Sidebar state
const isCollapse = ref(false);
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
};

// Header tabs
const headerTab = ref('home');

// Active menu
const activeMenu = computed(() => route.path);

// Theme toggle
const themeMode = computed(() => settingsStore.theme);
const isDarkMode = computed(() => themeMode.value === 'dark');
const toggleTheme = () => {
  const newTheme = themeMode.value === 'dark' ? 'light' : 'dark';
  settingsStore.setTheme(newTheme);
};

onMounted(() => {
  // Initialize settings
  settingsStore.fetchSettings();
});

// Open external link
const openExternalLink = async (url) => {
  try {
    await openUrl(url);
  } catch (error) {
    console.error('Failed to open URL:', error);
  }
};
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  width: var(--sidebar-width, 180px);
  height: 100%;
  transition: width 0.3s;
  background-color: var(--background-paper);
  border-right: 1px solid var(--border-color);
}

.sidebar.collapsed {
  width: 68px;
}

.logo-container {
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 68px;
  border-bottom: 1px solid var(--border-color);
  overflow: hidden;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  gap: 4px;
}

.logo-img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.logo-text {
  color: var(--primary-color);
  font-size: 24px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu {
  flex: 1;
  overflow-y: auto;
}

.menu-items {
  border-right: none;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--background-color);
}

.page-title {
  margin-left: 16px;
  font-size: 20px;
  font-weight: bold;
}

.theme-toggle-btn {
  font-size: 18px;
  color: var(--text-color);
}

.theme-toggle-btn:hover {
  color: var(--primary-color);
}

.content {
  flex: 1;
  /* padding: 20px; */
  overflow-y: auto;
}

/* 优化菜单悬浮颜色 */
.menu-items .el-menu-item:hover {
  background-color: var(--background-hover) !important;
  color: var(--primary-color) !important;
}

.menu-items .el-menu-item.is-active {
  background-color: rgba(76, 175, 80, 0.08) !important;
  color: var(--primary-color) !important;
}

html.dark .menu-items .el-menu-item:hover {
  background-color: var(--background-hover) !important;
  color: var(--primary-color) !important;
}

html.dark .menu-items .el-menu-item.is-active {
  background-color: rgba(76, 175, 80, 0.15) !important;
  color: var(--primary-color) !important;
}

/* 添加侧边栏底部样式 */
.sidebar-footer {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  border-top: 1px solid var(--border-color);
  .el-icon {
    font-size: 20px;
  }
}
</style> 