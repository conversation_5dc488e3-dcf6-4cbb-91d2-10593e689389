<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ElButton } from 'element-plus';
import { ref, onMounted } from 'vue';

const route = useRoute();
const router = useRouter();
const errorMessage = ref('应用程序发生错误');

onMounted(() => {
  // 如果路由中有错误消息，则显示该消息
  if (route.params.message) {
    errorMessage.value = route.params.message;
  }
});

// 返回首页
const goHome = () => {
  router.push({ name: 'Home' });
};

// 刷新页面
const refresh = () => {
  window.location.reload();
};
</script>

<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      <h1>出错了</h1>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <el-button type="primary" @click="refresh">刷新页面</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: var(--background-color);
  color: var(--text-color);
}

.error-container {
  text-align: center;
  padding: 2rem;
  max-width: 500px;
  background-color: var(--background-paper);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 4rem;
  color: #ff4d4f;
  margin-bottom: 1rem;
}

.error-message {
  margin: 1rem 0 2rem;
  font-size: 1.1rem;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}
</style> 