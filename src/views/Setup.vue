<template>
  <div class="setup-container">
    <div class="setup-card">
      <h1 class="setup-title">欢迎使用</h1>
      <p class="setup-description">请完成初始化设置</p>
      
      <div v-if="isLoading && !configResources.length" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载资源...</p>
      </div>
      
      <div v-else-if="!configResources.length" class="error-message">
        <p>未找到可用的资源源，请检查配置文件。</p>
      </div>
      
      <div v-else-if="success" class="success-message">
        <div class="checkmark">✓</div>
        <p>设置成功！即将进入首页...</p>
      </div>
      
      <div v-else class="setup-content">
        <!-- 步骤1：选择要导入的资源 -->
        <div v-if="step === 1" class="setup-step">
          <h2>第一步：选择要导入的资源</h2>
          <p class="setup-hint">请选择您想要导入的资源源</p>
          
          <div class="select-all-option">
            <input 
              type="checkbox" 
              id="select-all" 
              :checked="selectedResources.length === configResources.length" 
              @change="toggleSelectAll($event.target.checked)"
            />
            <label for="select-all">全选</label>
          </div>
          
          <div class="resources-list">
            <div 
              v-for="(resource, index) in configResources" 
              :key="index" 
              class="resource-item"
              :class="{ 'selected': selectedResources.includes(index) }"
            >
              <input 
                type="checkbox" 
                :id="`resource-${index}`" 
                :checked="selectedResources.includes(index)" 
                @change="toggleResourceSelection(index)"
              />
              <label :for="`resource-${index}`" class="resource-label">
                <span class="resource-name">{{ resource.name }}</span>
                <span class="resource-api">{{ resource.api }}</span>
              </label>
            </div>
          </div>
          
          <div class="button-group">
            <button 
              @click="nextStep" 
              class="next-btn" 
              :disabled="!canProceed"
            >
              下一步
            </button>
          </div>
        </div>
        
        <!-- 步骤2：选择默认源 -->
        <div v-else-if="step === 2" class="setup-step">
          <h2>第二步：选择默认源</h2>
          <p class="setup-hint">选择一个资源源作为默认源，后续可在设置中修改</p>
          
          <div class="default-source-selection">
            <div 
              v-for="index in selectedResources" 
              :key="index" 
              class="source-option"
            >
              <input 
                type="radio" 
                :id="`source-${index}`" 
                :value="index" 
                v-model="defaultSourceIndex"
                name="defaultSource"
              />
              <label :for="`source-${index}`" class="source-label">
                <span class="source-name">{{ configResources[index].name }}</span>
                <span class="source-api">{{ configResources[index].api }}</span>
              </label>
            </div>
          </div>
          
          <div v-if="error" class="error-message">{{ error }}</div>
          
          <div class="button-group">
            <button @click="prevStep" class="back-btn" :disabled="isLoading">返回</button>
            <button 
              @click="finishSetup" 
              class="finish-btn" 
              :disabled="defaultSourceIndex === null || isLoading"
            >
              <span v-if="isLoading">处理中...</span>
              <span v-else>完成设置</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useSetup } from '../composables/useSetup';

const { 
  configResources,
  selectedResources,
  defaultSourceIndex, 
  isLoading, 
  error, 
  success,
  step,
  canProceed,
  toggleResourceSelection,
  toggleSelectAll,
  nextStep,
  prevStep,
  finishSetup,
  checkInitialized
} = useSetup();

// 检查是否已初始化
onMounted(checkInitialized);
</script>

<style scoped>
.setup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--background-gradient);
  padding: 20px;
}

.setup-card {
  background-color: var(--background-paper);
  border-radius: var(--border-radius);
  padding: 30px;
  width: 100%;
  max-width: 600px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color-light);
}

.setup-title {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--text-color);
}

.setup-description {
  color: var(--text-color-secondary);
  margin-bottom: 24px;
}

.setup-hint {
  color: var(--text-color-secondary);
  margin-bottom: 16px;
  font-size: 0.9em;
}

.setup-content {
  animation: fadeIn 0.3s ease-in-out;
}

.setup-step {
  margin-bottom: 20px;
}

.select-all-option {
  margin-bottom: 15px;
  padding: 10px;
  background-color: var(--background-hover);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
}

.select-all-option label {
  margin-left: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.resources-list {
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.resource-item {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s;
}

.resource-item:last-child {
  border-bottom: none;
}

.resource-item:hover {
  background-color: var(--background-hover);
}

.resource-item.selected {
  background-color: rgba(76, 175, 80, 0.08);
}

.resource-label {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  flex: 1;
}

.default-source-selection {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.source-option {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s;
}

.source-option:last-child {
  border-bottom: none;
}

.source-option:hover {
  background-color: var(--background-hover);
}

.source-label {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.source-name {
  font-weight: 500;
  color: var(--text-color);
}

.source-api {
  font-size: 0.85em;
  color: var(--text-secondary);
  margin-top: 4px;
  word-break: break-all;
}

button {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.next-btn, .finish-btn {
  background-color: var(--primary-color);
  color: white;
  min-width: 120px;
}

.next-btn:hover:not(:disabled), .finish-btn:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--card-shadow);
}

.back-btn {
  background-color: var(--border-color);
  color: var(--text-color);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: var(--el-color-error);
  margin: 16px 0;
  padding: 12px;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: var(--border-radius);
}

.success-message {
  text-align: center;
  padding: 30px 20px;
  animation: fadeIn 0.5s ease-in-out;
}

.checkmark {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  font-size: 30px;
  margin-bottom: 20px;
  animation: scaleIn 0.3s ease-in-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

html.dark .loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary-color);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
</style> 