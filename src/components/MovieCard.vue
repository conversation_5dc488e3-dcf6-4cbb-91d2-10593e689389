<template>
  <div class="movie-card" @click="$emit('click', movie)">
    <div class="movie-poster">
      <el-image 
        :src="movie.vod_pic" 
        :alt="movie.vod_name"
        loading="lazy"
        fit="cover">
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
          </div>
        </template>
      </el-image>
      <div class="movie-overlay">
        <div class="movie-play-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
      </div>
      <span v-if="movie.vod_douban_score" class="movie-rating">{{ movie.vod_douban_score }}</span>
      <span v-if="movie.vod_remarks" class="movie-update-status">{{ movie.vod_remarks }}</span>
    </div>
    <div class="movie-info">
      <el-tooltip :content="movie.vod_name" placement="top" :show-after="500">
        <h3 class="movie-title">{{ movie.vod_name }}</h3>
      </el-tooltip>
      <p class="movie-year" v-if="movie.vod_year">{{ movie.vod_year }}</p>
      <div class="resource-tags">
        <div v-if="movie.resources && movie.resources.length > 0" class="resource-container">
          <template v-if="movie.resources.length <= 3">
            <div 
              v-for="resource in movie.resources" 
              :key="resource.sourceName"
              class="resource-tag"
            >
              {{ resource.sourceName }}
            </div>
          </template>
          <template v-else>
            <div 
              v-for="resource in movie.resources.slice(0, 2)" 
              :key="resource.sourceName"
              class="resource-tag"
            >
              {{ resource.sourceName }}
            </div>
            <el-popover
              placement="top"
              :width="120"
              trigger="hover"
              popper-class="resource-popover"
              :show-arrow="true"
            >
              <template #reference>
                <div class="resource-tag more-tag">
                  +{{ movie.resources.length - 2 }}
                  <span class="more-icon">▼</span>
                </div>
              </template>
              <template #default>
                <div class="popover-resources">
                  <div v-for="resource in movie.resources.slice(2)" :key="resource.sourceName" class="popover-resource-item">
                    {{ resource.sourceName }}
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoPlay, Picture } from '@element-plus/icons-vue';

defineProps({
  movie: {
    type: Object,
    required: true,
    validator: (value) => {
      return value.vod_name && value.vod_pic;
    }
  }
});
</script>

<style scoped>
.movie-card {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.movie-poster {
  position: relative;
  width: 100%;
  padding-top: 150%; /* 2:3 宽高比 */
  overflow: hidden;
}

.movie-poster :deep(.el-image) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.movie-poster :deep(.el-image__inner) {
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.movie-card:hover .movie-poster :deep(.el-image__inner) {
  transform: scale(1.05);
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

.image-error .el-icon {
  font-size: 32px;
}

.movie-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease;
}

.movie-card:hover .movie-overlay {
  opacity: 1;
}

.movie-play-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.movie-play-icon .el-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.movie-info {
  padding: 12px;
  background: #fff;
}

.movie-title {
  margin: 0 0 5px 0;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.movie-year {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.movie-rating {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
  z-index: 2;
}

.movie-update-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(130, 127, 127, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2;
}

.resource-tags {
  display: flex;
  margin-top: 8px;
}

.resource-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.resource-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary-light-7);
  transition: all 0.2s ease;
  white-space: nowrap;
}

.resource-tag:hover {
  background-color: var(--el-color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.more-tag {
  position: relative;
  cursor: pointer;
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border: 1px solid var(--el-color-info-light-7);
  display: flex;
  align-items: center;
  gap: 2px;
}

.more-icon {
  font-size: 8px;
  margin-top: 1px;
  transition: transform 0.3s ease;
}

.more-tag:hover .more-icon {
  transform: rotate(180deg);
}

.more-tag:hover {
  background-color: var(--el-color-info);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.popover-resources {
  max-height: 200px;
  overflow-y: auto;
}

.popover-resource-item {
  padding: 6px 0;
  font-size: 12px;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.popover-resource-item:last-child {
  border-bottom: none;
}

:deep(.resource-popover.el-popper) {
  padding: 8px 12px;
}
</style>