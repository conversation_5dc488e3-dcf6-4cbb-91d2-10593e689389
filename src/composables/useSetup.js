import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useSettingsStore } from '../stores/settingsStore';
import { getResources as getResourcesFromStore } from '../stores/resourceStore';
import { addResource } from '../db/resources';

export function useSetup() {
  const router = useRouter();
  const settingsStore = useSettingsStore();
  
  const configResources = ref([]); // 配置文件中的资源
  const selectedResources = ref([]); // 用户选择的资源
  const defaultSourceIndex = ref(null);
  const isLoading = ref(false);
  const error = ref(null);
  const success = ref(false);
  const step = ref(1); // 1: 选择资源, 2: 选择默认源

  // 检查是否可以进行下一步
  const canProceed = computed(() => {
    return selectedResources.value.length > 0;
  });

  // 加载配置文件中的资源
  async function loadConfigResources() {
    try {
      isLoading.value = true;
      // 从设置中获取资源
      const resources = await getResourcesFromStore()
      if (resources) {
        configResources.value = resources;
        // 默认全选
        selectedResources.value = configResources.value.map((_, index) => index);
      }
    } catch (err) {
      error.value = '加载资源失败: ' + err.message;
      console.error(err);
    } finally {
      isLoading.value = false;
    }
  }

  // 切换资源选择状态
  function toggleResourceSelection(index) {
    const position = selectedResources.value.indexOf(index);
    if (position === -1) {
      selectedResources.value.push(index);
    } else {
      selectedResources.value.splice(position, 1);
    }
  }

  // 全选/取消全选
  function toggleSelectAll(selectAll) {
    if (selectAll) {
      selectedResources.value = configResources.value.map((_, index) => index);
    } else {
      selectedResources.value = [];
    }
  }

  // 进入下一步
  function nextStep() {
    if (canProceed.value) {
      step.value = 2;
      // 默认选择第一个资源作为默认源
      defaultSourceIndex.value = selectedResources.value[0];
    }
  }

  // 返回上一步
  function prevStep() {
    step.value = 1;
  }

  // 完成设置
  async function finishSetup() {
    try {
      isLoading.value = true;
      error.value = null;
      
      // 将选中的资源保存到数据库，通过resourceStore
      for (const index of selectedResources.value) {
        const resource = configResources.value[index];
        const isDefault = index === defaultSourceIndex.value;
        
        await addResource({
          name: resource.name,
          api: resource.api,
          status: true,
          isDefault
        });
      }
      
      // 更新设置状态
      await settingsStore.fetchSettings();
      if (settingsStore.settings) {
        const updatedSettings = {
          ...settingsStore.settings,
          is_initialized: true
        };
        await settingsStore.saveSettings(updatedSettings);
        success.value = true;
        
        // 导航到首页
        setTimeout(() => {
          router.push('/');
        }, 1500);
      }
    } catch (err) {
      error.value = '设置失败: ' + err.message;
      console.error(err);
    } finally {
      isLoading.value = false;
    }
  }

  // 检查初始化状态
  async function checkInitialized() {
    await settingsStore.fetchSettings();
    if (settingsStore.isInitialized) {
      // 如果已初始化，跳转到首页
      router.push('/');
    } else {
      // 加载配置文件中的资源
      await loadConfigResources();
    }
  }

  return {
    configResources,
    selectedResources,
    defaultSourceIndex,
    isLoading,
    error,
    success,
    step,
    canProceed,
    loadConfigResources,
    toggleResourceSelection,
    toggleSelectAll,
    nextStep,
    prevStep,
    finishSetup,
    checkInitialized
  };
} 